/* 搜索和筛选区域样式 */
.search-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 40px 20px;
    margin-bottom: 24px;
}

.search-container {
    max-width: 1200px;
    margin: 0 auto;
}

.search-header {
    text-align: center;
    margin-bottom: 32px;
}

.search-header h2 {
    font-size: 28px;
    font-weight: 600;
    margin: 0 0 8px 0;
}

.search-header p {
    font-size: 16px;
    opacity: 0.9;
    margin: 0;
}

.search-form {
    position: relative;
    margin-bottom: 32px;
}

.search-input-group {
    display: flex;
    gap: 12px;
    max-width: 600px;
    margin: 0 auto;
}

.search-input-wrapper {
    flex: 1;
    position: relative;
    display: flex;
    align-items: center;
    background: white;
    border-radius: 12px;
    padding: 0 16px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.search-icon {
    font-size: 18px;
    color: #666;
    margin-right: 12px;
}

.search-input {
    flex: 1;
    border: none;
    outline: none;
    padding: 16px 0;
    font-size: 16px;
    color: #333;
    background: transparent;
}

.search-input::placeholder {
    color: #999;
}

.voice-search {
    background: none;
    border: none;
    font-size: 18px;
    color: #666;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: background-color 0.2s;
}

.voice-search:hover {
    background-color: #f5f5f5;
}

.search-btn {
    background: #4CAF50;
    color: white;
    border: none;
    border-radius: 12px;
    padding: 16px 24px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    box-shadow: 0 4px 12px rgba(76,175,80,0.3);
}

.search-btn:hover {
    background: #45a049;
    transform: translateY(-1px);
    box-shadow: 0 6px 16px rgba(76,175,80,0.4);
}

.search-suggestions {
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    width: 100%;
    max-width: 600px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 24px rgba(0,0,0,0.15);
    display: none;
    z-index: 1000;
    margin-top: 4px;
}

.search-suggestions.show {
    display: block;
    animation: fadeIn 0.2s ease-out;
}

.suggestion-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    cursor: pointer;
    transition: background-color 0.2s;
    border-bottom: 1px solid #f0f0f0;
}

.suggestion-item:hover {
    background-color: #f8f9fa;
}

.suggestion-item:last-child {
    border-bottom: none;
}

.suggestion-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.suggestion-name {
    font-weight: 500;
    color: #333;
}

.suggestion-type {
    font-size: 12px;
    color: #666;
    background: #f0f0f0;
    padding: 2px 6px;
    border-radius: 4px;
    width: fit-content;
}

.suggestion-price {
    font-weight: 600;
    color: #4CAF50;
}

.filter-section {
    margin-bottom: 24px;
}

.filter-tabs {
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-bottom: 24px;
    flex-wrap: wrap;
}

.filter-tab {
    background: rgba(255,255,255,0.2);
    color: white;
    border: 1px solid rgba(255,255,255,0.3);
    border-radius: 24px;
    padding: 8px 20px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

.filter-tab:hover,
.filter-tab.active {
    background: white;
    color: #667eea;
    border-color: white;
}

.advanced-filters {
    text-align: center;
}

.filter-toggle {
    background: rgba(255,255,255,0.2);
    color: white;
    border: 1px solid rgba(255,255,255,0.3);
    border-radius: 8px;
    padding: 12px 20px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0 auto;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

.filter-toggle:hover {
    background: rgba(255,255,255,0.3);
}

.toggle-icon {
    transition: transform 0.2s;
}

.filter-toggle[aria-expanded="true"] .toggle-icon {
    transform: rotate(180deg);
}

.filter-panel {
    background: rgba(255,255,255,0.95);
    color: #333;
    border-radius: 12px;
    padding: 24px;
    margin-top: 16px;
    display: none;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

.filter-panel.show {
    display: block;
    animation: slideDown 0.3s ease-out;
}

.filter-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.filter-group label {
    font-size: 14px;
    font-weight: 500;
    color: #555;
}

.filter-select,
.range-input input {
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    outline: none;
    transition: border-color 0.2s;
}

.filter-select:focus,
.range-input input:focus {
    border-color: #667eea;
}

.range-input {
    display: flex;
    align-items: center;
    gap: 8px;
}

.range-input input {
    flex: 1;
}

.filter-actions {
    display: flex;
    justify-content: center;
    gap: 12px;
    margin-top: 24px;
}

.reset-filters,
.apply-filters {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s;
}

.reset-filters {
    background: #f5f5f5;
    color: #666;
}

.reset-filters:hover {
    background: #e0e0e0;
}

.apply-filters {
    background: #4CAF50;
    color: white;
}

.apply-filters:hover {
    background: #45a049;
}

.quick-filters {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    flex-wrap: wrap;
}

.quick-filter-label {
    font-size: 14px;
    opacity: 0.9;
    white-space: nowrap;
}

.quick-filter-tags {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.quick-filter-tag {
    background: rgba(255,255,255,0.2);
    color: white;
    border: 1px solid rgba(255,255,255,0.3);
    border-radius: 16px;
    padding: 6px 14px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

.quick-filter-tag:hover {
    background: rgba(255,255,255,0.3);
    transform: translateY(-1px);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .search-section {
        padding: 24px 16px;
    }
    
    .search-header h2 {
        font-size: 24px;
    }
    
    .search-input-group {
        flex-direction: column;
    }
    
    .filter-tabs {
        justify-content: flex-start;
        overflow-x: auto;
        padding-bottom: 8px;
    }
    
    .filter-row {
        grid-template-columns: 1fr;
    }
    
    .quick-filters {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .quick-filter-tags {
        width: 100%;
    }
}
