<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能交易平台 - 专业的金融投资解决方案</title>
    <meta name="description" content="专业的智能交易平台，提供股票、基金、期货、数字货币等多种投资产品的交易服务">
    <meta name="keywords" content="交易平台,股票,基金,期货,数字货币,投资,金融">
    <link rel="icon" href="favicon.ico" type="image/x-icon">
    
    <!-- CSS文件 -->
    <link rel="stylesheet" href="css/global.css">
    <link rel="stylesheet" href="css/sidebar.css">
    <link rel="stylesheet" href="css/header.css">
    <link rel="stylesheet" href="css/search.css">
    <link rel="stylesheet" href="css/content.css">
</head>
<body>
    <!-- 移动端菜单按钮 -->
    <button class="mobile-menu-btn" onclick="window.tradeApp?.toggleSidebar()" aria-label="打开菜单">
        ☰
    </button>
    
    <!-- 移动端遮罩 -->
    <div class="mobile-overlay" onclick="window.tradeApp?.closeSidebar()"></div>
    
    <div class="main-container">
        <!-- 侧边栏 -->
        <nav class="sidebar-container" id="sidebar">
            <div id="sidebarContent">
                <!-- 侧边栏内容将通过JavaScript加载 -->
            </div>
        </nav>
        
        <!-- 主内容区 -->
        <main class="main-content">
            <!-- 顶部横幅 -->
            <div id="headerBanner">
                <!-- 顶部横幅内容将通过JavaScript加载 -->
            </div>
            
            <!-- 内容包装器 -->
            <div class="content-wrapper">
                <!-- 搜索区域 -->
                <section id="searchSection" aria-label="搜索和筛选">
                    <!-- 搜索内容将通过JavaScript加载 -->
                </section>
                
                <!-- 主要内容 -->
                <section class="container">
                    <div id="contentList" aria-label="市场数据列表">
                        <!-- 内容列表将通过JavaScript加载 -->
                    </div>
                </section>
            </div>
        </main>
    </div>

    <!-- JavaScript文件 -->
    <script src="js/app.js"></script>
    <script src="js/sidebar.js"></script>
    <script src="js/header.js"></script>
    <script src="js/search.js"></script>
    <script src="js/content.js"></script>
</body>
</html>
