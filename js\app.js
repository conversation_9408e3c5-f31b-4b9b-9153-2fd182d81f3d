// 全局应用管理器
class TradeApp {
    constructor() {
        this.isMobile = window.innerWidth <= 768;
        this.sidebarOpen = false;
        this.modules = new Map();
        this.init();
    }

    // 初始化应用
    async init() {
        this.setupEventListeners();
        this.checkMobileView();
        
        try {
            await this.loadAllModules();
            this.showSuccessMessage('应用加载完成');
            console.log('Trade App initialized successfully');
        } catch (error) {
            console.error('App initialization failed:', error);
            this.showErrorMessage('应用初始化失败，请刷新页面重试');
        }
    }

    // 加载所有模块
    async loadAllModules() {
        const moduleConfigs = [
            { name: 'sidebar', containerId: 'sidebarContent', scriptPath: 'js/sidebar.js' },
            { name: 'header', containerId: 'headerBanner', scriptPath: 'js/header.js' },
            { name: 'search', containerId: 'searchSection', scriptPath: 'js/search.js' },
            { name: 'content', containerId: 'contentList', scriptPath: 'js/content.js' }
        ];

        const loadPromises = moduleConfigs.map(config => this.loadModule(config));
        await Promise.all(loadPromises);
    }

    // 加载单个模块
    async loadModule(config) {
        try {
            // 加载HTML模板
            const template = await this.loadTemplate(config.name);
            const container = document.getElementById(config.containerId);
            
            if (container && template) {
                container.innerHTML = template;
            }

            // 动态加载并执行JavaScript模块
            if (config.scriptPath) {
                await this.loadScript(config.scriptPath);
            }

            this.modules.set(config.name, {
                loaded: true,
                container: container
            });

        } catch (error) {
            console.error(`Failed to load module ${config.name}:`, error);
            this.showModuleError(config.name, config.containerId);
        }
    }

    // 加载HTML模板
    async loadTemplate(moduleName) {
        const templateMap = {
            sidebar: this.getSidebarTemplate(),
            header: this.getHeaderTemplate(),
            search: this.getSearchTemplate(),
            content: this.getContentTemplate()
        };

        return templateMap[moduleName] || '';
    }

    // 加载JavaScript文件
    loadScript(src) {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = src;
            script.onload = resolve;
            script.onerror = reject;
            document.head.appendChild(script);
        });
    }

    // 设置事件监听器
    setupEventListeners() {
        // 窗口大小改变
        window.addEventListener('resize', () => this.checkMobileView());
        
        // 键盘导航
        document.addEventListener('keydown', (e) => this.handleKeydown(e));
        
        // 点击外部关闭侧边栏
        document.addEventListener('click', (e) => this.handleOutsideClick(e));
        
        // 全局错误处理
        window.addEventListener('error', (e) => this.handleGlobalError(e));
        window.addEventListener('unhandledrejection', (e) => this.handleUnhandledRejection(e));
    }

    // 检查移动端视图
    checkMobileView() {
        const newIsMobile = window.innerWidth <= 768;
        
        if (newIsMobile !== this.isMobile) {
            this.isMobile = newIsMobile;
            
            if (!this.isMobile && this.sidebarOpen) {
                this.closeSidebar();
            }
        }
    }

    // 处理键盘事件
    handleKeydown(e) {
        if (e.key === 'Escape' && this.sidebarOpen) {
            this.closeSidebar();
        }
    }

    // 处理外部点击
    handleOutsideClick(e) {
        if (this.isMobile && this.sidebarOpen && 
            !e.target.closest('.sidebar-container') && 
            !e.target.closest('.mobile-menu-btn')) {
            this.closeSidebar();
        }
    }

    // 全局错误处理
    handleGlobalError(e) {
        console.error('Global error:', e.error);
        this.showErrorMessage('发生了一个错误，请刷新页面重试');
    }

    // 处理未捕获的Promise拒绝
    handleUnhandledRejection(e) {
        console.error('Unhandled promise rejection:', e.reason);
        this.showErrorMessage('数据加载失败，请检查网络连接');
    }

    // 侧边栏控制
    toggleSidebar() {
        if (this.sidebarOpen) {
            this.closeSidebar();
        } else {
            this.openSidebar();
        }
    }

    openSidebar() {
        if (!this.isMobile) return;
        
        const sidebar = document.getElementById('sidebar');
        const overlay = document.querySelector('.mobile-overlay');
        
        if (sidebar && overlay) {
            sidebar.classList.add('show');
            overlay.classList.add('show');
            this.sidebarOpen = true;
            document.body.style.overflow = 'hidden';
        }
    }

    closeSidebar() {
        if (!this.isMobile) return;
        
        const sidebar = document.getElementById('sidebar');
        const overlay = document.querySelector('.mobile-overlay');
        
        if (sidebar && overlay) {
            sidebar.classList.remove('show');
            overlay.classList.remove('show');
            this.sidebarOpen = false;
            document.body.style.overflow = '';
        }
    }

    // 消息提示
    showErrorMessage(message) {
        this.showMessage(message, 'error');
    }

    showSuccessMessage(message) {
        this.showMessage(message, 'success');
    }

    showMessage(message, type = 'info') {
        const messageDiv = document.createElement('div');
        messageDiv.className = `${type}-message`;
        messageDiv.innerHTML = `<strong>${type === 'error' ? '错误' : '成功'}：</strong>${message}`;
        
        document.body.insertBefore(messageDiv, document.body.firstChild);
        
        const timeout = type === 'error' ? 5000 : 3000;
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.parentNode.removeChild(messageDiv);
            }
        }, timeout);
    }

    // 显示模块加载错误
    showModuleError(moduleName, containerId) {
        const container = document.getElementById(containerId);
        if (container) {
            container.innerHTML = `
                <div class="error-message">
                    <strong>模块加载失败</strong><br>
                    无法加载 ${moduleName} 模块，请检查网络连接后重试。
                    <button onclick="location.reload()" style="margin-left: 12px; padding: 4px 8px; background: #1976d2; color: white; border: none; border-radius: 4px; cursor: pointer;">
                        重新加载
                    </button>
                </div>
            `;
        }
    }

    // 获取模块实例
    getModule(name) {
        return this.modules.get(name);
    }

    // HTML模板定义
    getSidebarTemplate() {
        return `
            <div class="sidebar">
                <div class="sidebar-header">
                    <div class="user-profile">
                        <img src="https://via.placeholder.com/48" alt="用户头像" class="user-avatar">
                        <div class="user-info">
                            <h3>张三</h3>
                            <p>高级投资者</p>
                        </div>
                    </div>
                    <div class="user-stats">
                        <div class="stat-item">
                            <span class="stat-value">￥128.5K</span>
                            <span class="stat-label">总资产</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value">+12.5%</span>
                            <span class="stat-label">今日收益</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value">23</span>
                            <span class="stat-label">持仓</span>
                        </div>
                    </div>
                </div>
                
                <div class="sidebar-nav">
                    <div class="nav-section">
                        <h4 class="nav-title">交易</h4>
                        <ul class="nav-list">
                            <li class="nav-item">
                                <a href="#dashboard" class="nav-link active">
                                    <span class="nav-icon">📊</span>
                                    仪表盘
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="#markets" class="nav-link">
                                    <span class="nav-icon">📈</span>
                                    市场行情
                                    <span class="nav-badge">New</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="#portfolio" class="nav-link">
                                    <span class="nav-icon">💼</span>
                                    我的投资组合
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="#orders" class="nav-link">
                                    <span class="nav-icon">📋</span>
                                    交易订单
                                    <span class="nav-badge">3</span>
                                </a>
                            </li>
                        </ul>
                    </div>
                    
                    <div class="nav-section">
                        <h4 class="nav-title">分析</h4>
                        <ul class="nav-list">
                            <li class="nav-item">
                                <a href="#analysis" class="nav-link">
                                    <span class="nav-icon">🔍</span>
                                    技术分析
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="#news" class="nav-link">
                                    <span class="nav-icon">📰</span>
                                    财经资讯
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="#research" class="nav-link">
                                    <span class="nav-icon">🎯</span>
                                    投研报告
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
                
                <div class="sidebar-footer">
                    <div class="quick-actions">
                        <button class="quick-action-btn">快速买入</button>
                        <button class="quick-action-btn">快速卖出</button>
                    </div>
                    
                    <div class="theme-toggle">
                        <span>夜间模式</span>
                        <div class="toggle-switch" onclick="window.tradeApp.toggleTheme()"></div>
                    </div>
                </div>
            </div>
        `;
    }

    getHeaderTemplate() {
        return `
            <div class="header-banner">
                <div class="top-banner">
                    <span class="banner-text">🎉 新用户注册送500积分！</span>
                    <button class="banner-close" onclick="window.tradeApp.closeBanner()" aria-label="关闭横幅">×</button>
                </div>
                
                <header class="main-header">
                    <div class="header-container">
                        <div class="logo-section">
                            <img src="https://via.placeholder.com/32" alt="交易平台" class="logo">
                            <h1>智能交易平台</h1>
                        </div>
                        
                        <nav class="main-nav">
                            <ul>
                                <li><a href="#home" class="nav-link active">首页</a></li>
                                <li><a href="#markets" class="nav-link">市场</a></li>
                                <li><a href="#trading" class="nav-link">交易</a></li>
                                <li><a href="#portfolio" class="nav-link">投资组合</a></li>
                                <li><a href="#news" class="nav-link">资讯</a></li>
                            </ul>
                        </nav>
                        
                        <div class="user-actions">
                            <button class="notification-btn" onclick="window.tradeApp.toggleNotifications()" aria-label="通知">
                                <span class="icon">🔔</span>
                                <span class="badge">3</span>
                            </button>
                            <div class="user-menu">
                                <button class="user-avatar" onclick="window.tradeApp.toggleUserMenu()" aria-label="用户菜单">
                                    <img src="https://via.placeholder.com/40" alt="用户头像">
                                </button>
                                <div class="dropdown-menu" id="userDropdown">
                                    <a href="#profile">个人资料</a>
                                    <a href="#settings">设置</a>
                                    <a href="#help">帮助</a>
                                    <hr>
                                    <a href="#logout">退出登录</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </header>
            </div>
        `;
    }

    getSearchTemplate() {
        return `
            <div class="search-section">
                <div class="search-container">
                    <div class="search-header">
                        <h2>发现优质交易机会</h2>
                        <p>使用智能筛选找到最适合的投资产品</p>
                    </div>
                    
                    <div class="search-form">
                        <div class="search-input-group">
                            <div class="search-input-wrapper">
                                <span class="search-icon">🔍</span>
                                <input type="text" id="searchInput" placeholder="搜索股票、基金、期货..." class="search-input" autocomplete="off">
                                <button type="button" class="voice-search" onclick="window.tradeApp.startVoiceSearch()" aria-label="语音搜索">🎤</button>
                            </div>
                            <button type="button" class="search-btn" onclick="window.tradeApp.performSearch()">搜索</button>
                        </div>
                        <div class="search-suggestions" id="searchSuggestions"></div>
                    </div>
                    
                    <div class="filter-section">
                        <div class="filter-tabs">
                            <button class="filter-tab active" data-category="all" onclick="window.tradeApp.switchCategory('all')">全部</button>
                            <button class="filter-tab" data-category="stocks" onclick="window.tradeApp.switchCategory('stocks')">股票</button>
                            <button class="filter-tab" data-category="funds" onclick="window.tradeApp.switchCategory('funds')">基金</button>
                            <button class="filter-tab" data-category="futures" onclick="window.tradeApp.switchCategory('futures')">期货</button>
                            <button class="filter-tab" data-category="crypto" onclick="window.tradeApp.switchCategory('crypto')">数字货币</button>
                        </div>
                        
                        <div class="advanced-filters">
                            <button class="filter-toggle" onclick="window.tradeApp.toggleAdvancedFilters()" aria-expanded="false">
                                高级筛选 <span class="toggle-icon">▼</span>
                            </button>
                            <div class="filter-panel" id="advancedFilters">
                                <!-- 高级筛选内容 -->
                            </div>
                        </div>
                    </div>
                    
                    <div class="quick-filters">
                        <span class="quick-filter-label">热门筛选：</span>
                        <div class="quick-filter-tags">
                            <button class="quick-filter-tag" onclick="window.tradeApp.applyQuickFilter('热门股票')">热门股票</button>
                            <button class="quick-filter-tag" onclick="window.tradeApp.applyQuickFilter('新股上市')">新股上市</button>
                            <button class="quick-filter-tag" onclick="window.tradeApp.applyQuickFilter('高分红')">高分红</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    getContentTemplate() {
        return `
            <div class="content-list">
                <div class="content-container">
                    <div class="list-header">
                        <div class="header-left">
                            <h2>市场动态</h2>
                            <span class="result-count" id="resultCount">共找到 1,234 个结果</span>
                        </div>
                        <div class="header-controls">
                            <div class="view-controls">
                                <button class="view-btn active" data-view="list" onclick="window.tradeApp.switchView('list')" aria-label="列表视图">☰</button>
                                <button class="view-btn" data-view="grid" onclick="window.tradeApp.switchView('grid')" aria-label="网格视图">⊞</button>
                                <button class="view-btn" data-view="chart" onclick="window.tradeApp.switchView('chart')" aria-label="图表视图">📊</button>
                            </div>
                            <div class="sort-controls">
                                <select id="sortBy" class="sort-select" onchange="window.tradeApp.sortResults()" title="排序方式">
                                    <option value="relevance">相关度</option>
                                    <option value="price_asc">价格升序</option>
                                    <option value="price_desc">价格降序</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="content-body">
                        <div class="content-grid" id="contentGrid"></div>
                        <div class="loading-indicator hide" id="loadingIndicator">
                            <div class="spinner"></div>
                            <span>加载中...</span>
                        </div>
                        <div class="empty-state hide" id="emptyState">
                            <div class="empty-icon">📋</div>
                            <h3>暂无数据</h3>
                            <p>请尝试调整筛选条件或搜索关键词</p>
                        </div>
                    </div>
                    
                    <div class="pagination-section">
                        <div class="pagination-info">显示第 <span id="currentRange">1-20</span> 条，共 <span id="totalItems">1,234</span> 条</div>
                        <div class="pagination-controls" id="paginationControls"></div>
                    </div>
                </div>
            </div>
        `;
    }
}

// 创建全局应用实例
window.tradeApp = new TradeApp();

// 确保DOM加载完成后初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        // App已经在构造函数中初始化
    });
} else {
    // DOM已经加载完成
}
