/* 侧边栏样式 */
.sidebar {
    height: 100%;
    background: white;
    display: flex;
    flex-direction: column;
}

.sidebar-header {
    padding: 24px 20px;
    border-bottom: 1px solid #e0e0e0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.user-profile {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
}

.user-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    border: 2px solid rgba(255,255,255,0.3);
    object-fit: cover;
}

.user-info h3 {
    font-size: 16px;
    font-weight: 600;
    margin: 0 0 4px 0;
}

.user-info p {
    font-size: 12px;
    opacity: 0.8;
    margin: 0;
}

.user-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
    margin-top: 16px;
}

.stat-item {
    text-align: center;
}

.stat-value {
    display: block;
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 4px;
}

.stat-label {
    font-size: 11px;
    opacity: 0.8;
}

.sidebar-nav {
    flex: 1;
    padding: 24px 0;
    overflow-y: auto;
}

.nav-section {
    margin-bottom: 32px;
}

.nav-section:last-child {
    margin-bottom: 0;
}

.nav-title {
    font-size: 12px;
    font-weight: 600;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    padding: 0 20px 12px 20px;
    margin: 0;
}

.nav-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-item {
    margin: 2px 0;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: #666;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s;
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    background: #f8f9fa;
    color: #1976d2;
}

.nav-link.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: #1976d2;
}

.nav-icon {
    font-size: 18px;
    margin-right: 12px;
    width: 24px;
    text-align: center;
}

.nav-badge {
    background: #f44336;
    color: white;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 10px;
    margin-left: auto;
    min-width: 18px;
    text-align: center;
}

.sidebar-footer {
    padding: 20px;
    border-top: 1px solid #e0e0e0;
}

.quick-actions {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
    margin-bottom: 16px;
}

.quick-action-btn {
    background: #f8f9fa;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 12px;
    color: #666;
    cursor: pointer;
    transition: all 0.2s;
    text-align: center;
}

.quick-action-btn:hover {
    background: #e9ecef;
    border-color: #ccc;
    color: #333;
}

.theme-toggle {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 0;
}

.theme-toggle span {
    font-size: 14px;
    color: #666;
}

.toggle-switch {
    position: relative;
    width: 40px;
    height: 20px;
    background: #ccc;
    border-radius: 10px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.toggle-switch.active {
    background: #1976d2;
}

.toggle-switch::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 16px;
    height: 16px;
    background: white;
    border-radius: 50%;
    transition: transform 0.2s;
}

.toggle-switch.active::before {
    transform: translateX(20px);
}

/* 历史记录和关注列表 */
.recent-section {
    padding: 0 20px;
}

.recent-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.recent-item {
    display: flex;
    align-items: center;
    padding: 8px 0;
    font-size: 12px;
    color: #666;
    border-bottom: 1px solid #f0f0f0;
}

.recent-item:last-child {
    border-bottom: none;
}

.recent-item .symbol {
    font-weight: 600;
    color: #333;
    margin-right: 8px;
}

.recent-item .price {
    margin-left: auto;
    font-weight: 500;
}

.recent-item .change {
    margin-left: 4px;
    font-size: 10px;
}

.recent-item .change.positive {
    color: #4CAF50;
}

.recent-item .change.negative {
    color: #f44336;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .user-stats {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .quick-actions {
        grid-template-columns: 1fr;
    }
}
