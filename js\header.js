// 头部模块
class HeaderModule {
    constructor() {
        this.userMenuOpen = false;
        this.notifications = [];
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadNotifications();
    }

    setupEventListeners() {
        // 点击外部关闭下拉菜单
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.user-menu')) {
                this.closeUserMenu();
            }
        });

        // 导航链接点击
        document.addEventListener('click', (e) => {
            if (e.target.matches('.main-nav .nav-link')) {
                this.handleMainNavClick(e);
            }
        });
    }

    // 关闭横幅
    closeBanner() {
        const banner = document.querySelector('.top-banner');
        if (banner) {
            banner.style.animation = 'slideUp 0.3s ease-out forwards';
            setTimeout(() => {
                banner.style.display = 'none';
            }, 300);
        }
    }

    // 切换通知面板
    toggleNotifications() {
        console.log('Toggle notifications');
        // 这里可以实现通知面板的显示/隐藏
        // 实际项目中可以调用API获取通知数据
        
        if (window.tradeApp) {
            window.tradeApp.showSuccessMessage('通知功能开发中');
        }
    }

    // 切换用户菜单
    toggleUserMenu() {
        const dropdown = document.getElementById('userDropdown');
        if (dropdown) {
            this.userMenuOpen = !this.userMenuOpen;
            dropdown.classList.toggle('show', this.userMenuOpen);
        }
    }

    // 关闭用户菜单
    closeUserMenu() {
        const dropdown = document.getElementById('userDropdown');
        if (dropdown && this.userMenuOpen) {
            dropdown.classList.remove('show');
            this.userMenuOpen = false;
        }
    }

    // 处理主导航点击
    handleMainNavClick(e) {
        e.preventDefault();
        
        // 移除所有active状态
        document.querySelectorAll('.main-nav .nav-link').forEach(link => {
            link.classList.remove('active');
        });
        
        // 添加当前active状态
        e.target.classList.add('active');
        
        const href = e.target.getAttribute('href');
        console.log('Main navigation to:', href);
        
        // 根据导航项执行相应操作
        this.handleNavigation(href);
    }

    // 处理导航逻辑
    handleNavigation(href) {
        const navMap = {
            '#home': () => this.goToHome(),
            '#markets': () => this.goToMarkets(),
            '#trading': () => this.goToTrading(),
            '#portfolio': () => this.goToPortfolio(),
            '#news': () => this.goToNews()
        };

        const handler = navMap[href];
        if (handler) {
            handler();
        }
    }

    goToHome() {
        console.log('Navigate to Home');
        if (window.tradeApp) {
            window.tradeApp.showSuccessMessage('欢迎回到首页');
        }
    }

    goToMarkets() {
        console.log('Navigate to Markets');
        // 可以触发市场数据更新
        this.updateMarketData();
    }

    goToTrading() {
        console.log('Navigate to Trading');
        if (window.tradeApp) {
            window.tradeApp.showSuccessMessage('交易功能开发中');
        }
    }

    goToPortfolio() {
        console.log('Navigate to Portfolio');
        if (window.tradeApp) {
            window.tradeApp.showSuccessMessage('投资组合功能开发中');
        }
    }

    goToNews() {
        console.log('Navigate to News');
        if (window.tradeApp) {
            window.tradeApp.showSuccessMessage('资讯功能开发中');
        }
    }

    // 更新市场数据
    async updateMarketData() {
        try {
            // 模拟API调用
            const marketData = await this.fetchMarketData();
            console.log('Market data updated:', marketData);
            
            if (window.tradeApp) {
                window.tradeApp.showSuccessMessage('市场数据已更新');
            }
        } catch (error) {
            console.error('Failed to update market data:', error);
            if (window.tradeApp) {
                window.tradeApp.showErrorMessage('市场数据更新失败');
            }
        }
    }

    // 获取市场数据
    async fetchMarketData() {
        return new Promise(resolve => {
            setTimeout(() => {
                resolve({
                    timestamp: Date.now(),
                    markets: ['股票', '基金', '期货', '数字货币']
                });
            }, 1000);
        });
    }

    // 加载通知数据
    async loadNotifications() {
        try {
            this.notifications = await this.fetchNotifications();
            this.updateNotificationBadge();
        } catch (error) {
            console.error('Failed to load notifications:', error);
        }
    }

    // 获取通知数据
    async fetchNotifications() {
        return new Promise(resolve => {
            setTimeout(() => {
                resolve([
                    { id: 1, type: 'trade', message: '您的苹果股票订单已执行' },
                    { id: 2, type: 'alert', message: '特斯拉股价达到预设价位' },
                    { id: 3, type: 'news', message: '美联储利率决议即将公布' }
                ]);
            }, 500);
        });
    }

    // 更新通知徽章
    updateNotificationBadge() {
        const badge = document.querySelector('.notification-btn .badge');
        if (badge) {
            const unreadCount = this.notifications.filter(n => !n.read).length;
            badge.textContent = unreadCount;
            badge.style.display = unreadCount > 0 ? 'block' : 'none';
        }
    }

    // 标记通知为已读
    markNotificationAsRead(notificationId) {
        const notification = this.notifications.find(n => n.id === notificationId);
        if (notification) {
            notification.read = true;
            this.updateNotificationBadge();
        }
    }

    // 添加新通知
    addNotification(notification) {
        this.notifications.unshift({
            id: Date.now(),
            ...notification,
            read: false,
            timestamp: new Date()
        });
        this.updateNotificationBadge();
    }

    // 搜索功能（从头部搜索框）
    performHeaderSearch(query) {
        console.log('Header search:', query);
        // 可以与搜索模块集成
        if (window.searchModule) {
            window.searchModule.performSearch(query);
        }
    }
}

// 导出模块供全局使用
window.HeaderModule = HeaderModule;

// 将方法暴露到全局对象
if (window.tradeApp) {
    window.tradeApp.closeBanner = function() {
        if (window.headerModule) {
            window.headerModule.closeBanner();
        }
    };

    window.tradeApp.toggleNotifications = function() {
        if (window.headerModule) {
            window.headerModule.toggleNotifications();
        }
    };

    window.tradeApp.toggleUserMenu = function() {
        if (window.headerModule) {
            window.headerModule.toggleUserMenu();
        }
    };
}

// 初始化头部模块
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.headerModule = new HeaderModule();
    });
} else {
    window.headerModule = new HeaderModule();
}
