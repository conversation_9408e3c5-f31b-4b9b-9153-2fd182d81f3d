<!-- 顶部横幅模块 -->
<div class="header-banner">
    <div class="top-banner">
        <span class="banner-text">🎉 新用户注册送500积分！</span>
        <button class="banner-close" onclick="closeBanner()" aria-label="关闭横幅">×</button>
    </div>
    
    <header class="main-header">
        <div class="header-container">
            <div class="logo-section">
                <img src="logo.png" alt="交易平台" class="logo">
                <h1>智能交易平台</h1>
            </div>
            
            <nav class="main-nav">
                <ul>
                    <li><a href="#home" class="nav-link active">首页</a></li>
                    <li><a href="#markets" class="nav-link">市场</a></li>
                    <li><a href="#trading" class="nav-link">交易</a></li>
                    <li><a href="#portfolio" class="nav-link">投资组合</a></li>
                    <li><a href="#news" class="nav-link">资讯</a></li>
                </ul>
            </nav>
            
            <div class="user-actions">
                <button class="notification-btn" onclick="toggleNotifications()" aria-label="通知">
                    <span class="icon">🔔</span>
                    <span class="badge">3</span>
                </button>
                <div class="user-menu">
                    <button class="user-avatar" onclick="toggleUserMenu()" aria-label="用户菜单">
                        <img src="avatar.jpg" alt="用户头像">
                    </button>
                    <div class="dropdown-menu" id="userDropdown">
                        <a href="#profile">个人资料</a>
                        <a href="#settings">设置</a>
                        <a href="#help">帮助</a>
                        <hr>
                        <a href="#logout">退出登录</a>
                    </div>
                </div>
            </div>
        </div>
    </header>
</div>

<style>
.header-banner {
    position: sticky;
    top: 0;
    z-index: 1000;
    background: #fff;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.top-banner {
    background: linear-gradient(90deg, #4CAF50, #45a049);
    color: white;
    padding: 8px 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    animation: slideDown 0.5s ease-out;
}

.banner-text {
    font-size: 14px;
    font-weight: 500;
}

.banner-close {
    position: absolute;
    right: 20px;
    background: none;
    border: none;
    color: white;
    font-size: 18px;
    cursor: pointer;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
}

.banner-close:hover {
    background-color: rgba(255,255,255,0.2);
}

.main-header {
    background: white;
    border-bottom: 1px solid #e0e0e0;
}

.header-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    align-items: center;
    height: 64px;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 12px;
}

.logo {
    width: 32px;
    height: 32px;
    border-radius: 6px;
}

.logo-section h1 {
    font-size: 20px;
    font-weight: 600;
    color: #1976d2;
    margin: 0;
}

.main-nav {
    flex: 1;
    margin-left: 40px;
}

.main-nav ul {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 32px;
}

.nav-link {
    text-decoration: none;
    color: #666;
    font-weight: 500;
    padding: 8px 0;
    position: relative;
    transition: color 0.2s;
}

.nav-link:hover,
.nav-link.active {
    color: #1976d2;
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -16px;
    left: 0;
    right: 0;
    height: 2px;
    background: #1976d2;
}

.user-actions {
    display: flex;
    align-items: center;
    gap: 16px;
}

.notification-btn {
    position: relative;
    background: none;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s;
}

.notification-btn:hover {
    background-color: #f5f5f5;
    border-color: #ccc;
}

.notification-btn .badge {
    position: absolute;
    top: -6px;
    right: -6px;
    background: #f44336;
    color: white;
    border-radius: 10px;
    font-size: 10px;
    padding: 2px 6px;
    min-width: 16px;
    text-align: center;
}

.user-menu {
    position: relative;
}

.user-avatar {
    background: none;
    border: 2px solid #e0e0e0;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    padding: 0;
    cursor: pointer;
    overflow: hidden;
    transition: border-color 0.2s;
}

.user-avatar:hover {
    border-color: #1976d2;
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.dropdown-menu {
    position: absolute;
    top: 48px;
    right: 0;
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    min-width: 160px;
    display: none;
    z-index: 1001;
}

.dropdown-menu.show {
    display: block;
    animation: fadeIn 0.2s ease-out;
}

.dropdown-menu a {
    display: block;
    padding: 12px 16px;
    text-decoration: none;
    color: #333;
    transition: background-color 0.2s;
}

.dropdown-menu a:hover {
    background-color: #f5f5f5;
}

.dropdown-menu hr {
    margin: 8px 0;
    border: none;
    border-top: 1px solid #e0e0e0;
}

@keyframes slideDown {
    from {
        transform: translateY(-100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-8px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .header-container {
        padding: 0 16px;
    }
    
    .main-nav {
        display: none;
    }
    
    .logo-section h1 {
        font-size: 18px;
    }
    
    .user-actions {
        gap: 12px;
    }
}
</style>

<script>
// 顶部横幅功能
function closeBanner() {
    const banner = document.querySelector('.top-banner');
    if (banner) {
        banner.style.animation = 'slideUp 0.3s ease-out forwards';
        setTimeout(() => {
            banner.style.display = 'none';
        }, 300);
    }
}

// 通知功能
function toggleNotifications() {
    // 这里可以实现通知面板的显示/隐藏
    console.log('切换通知面板');
    // 实际项目中可以调用API获取通知数据
}

// 用户菜单功能
function toggleUserMenu() {
    const dropdown = document.getElementById('userDropdown');
    if (dropdown) {
        dropdown.classList.toggle('show');
    }
}

// 点击外部关闭下拉菜单
document.addEventListener('click', function(event) {
    const userMenu = document.querySelector('.user-menu');
    const dropdown = document.getElementById('userDropdown');
    
    if (dropdown && !userMenu.contains(event.target)) {
        dropdown.classList.remove('show');
    }
});

// 导航切换功能
function switchNavigation(targetId) {
    // 移除所有active状态
    document.querySelectorAll('.nav-link').forEach(link => {
        link.classList.remove('active');
    });
    
    // 添加当前active状态
    const targetLink = document.querySelector(`a[href="#${targetId}"]`);
    if (targetLink) {
        targetLink.classList.add('active');
    }
}

// 添加CSS动画样式
const style = document.createElement('style');
style.textContent = `
    @keyframes slideUp {
        from {
            transform: translateY(0);
            opacity: 1;
        }
        to {
            transform: translateY(-100%);
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);
</script>
