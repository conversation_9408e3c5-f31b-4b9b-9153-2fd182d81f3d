<!-- 内容列表模块 -->
<div class="content-list">
    <div class="content-container">
        <div class="list-header">
            <div class="header-left">
                <h2>市场动态</h2>
                <span class="result-count" id="resultCount">共找到 1,234 个结果</span>
            </div>
            <div class="header-controls">
                <div class="view-controls">
                    <button class="view-btn active" data-view="list" onclick="switchView('list')" aria-label="列表视图">
                        ☰
                    </button>
                    <button class="view-btn" data-view="grid" onclick="switchView('grid')" aria-label="网格视图">
                        ⊞
                    </button>
                    <button class="view-btn" data-view="chart" onclick="switchView('chart')" aria-label="图表视图">
                        📊
                    </button>
                </div>
                <div class="sort-controls">
                    <select id="sortBy" class="sort-select" title="排序方式" onchange="sortResults()">
                        <option value="relevance">相关度</option>
                        <option value="price_asc">价格升序</option>
                        <option value="price_desc">价格降序</option>
                        <option value="change_asc">涨跌幅升序</option>
                        <option value="change_desc">涨跌幅降序</option>
                        <option value="volume_desc">成交量降序</option>
                        <option value="name_asc">名称A-Z</option>
                    </select>
                </div>
            </div>
        </div>
        
        <div class="content-body">
            <div class="content-grid" id="contentGrid">
                <!-- 动态内容将加载到这里 -->
            </div>
            
            <div class="loading-indicator" id="loadingIndicator">
                <div class="spinner"></div>
                <span>加载中...</span>
            </div>
            
            <div class="empty-state hide" id="emptyState">
                <div class="empty-icon">📋</div>
                <h3>暂无数据</h3>
                <p>请尝试调整筛选条件或搜索关键词</p>
                <button class="retry-btn" onclick="retryLoad()">重新加载</button>
            </div>
        </div>
        
        <div class="pagination-section">
            <div class="pagination-info">
                显示第 <span id="currentRange">1-20</span> 条，共 <span id="totalItems">1,234</span> 条
            </div>
            <div class="pagination-controls" id="paginationControls">
                <!-- 分页控件将动态生成 -->
            </div>
        </div>
    </div>
</div>

<style>
.content-list {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    margin-bottom: 24px;
    overflow: hidden;
}

.content-container {
    padding: 24px;
}

.list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    flex-wrap: wrap;
    gap: 16px;
}

.header-left h2 {
    font-size: 24px;
    font-weight: 600;
    color: #333;
    margin: 0 0 4px 0;
}

.result-count {
    font-size: 14px;
    color: #666;
}

.header-controls {
    display: flex;
    align-items: center;
    gap: 16px;
}

.view-controls {
    display: flex;
    background: #f5f5f5;
    border-radius: 8px;
    padding: 4px;
}

.view-btn {
    background: none;
    border: none;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 16px;
    transition: all 0.2s;
    color: #666;
}

.view-btn:hover,
.view-btn.active {
    background: white;
    color: #1976d2;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.sort-select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background: white;
    cursor: pointer;
    outline: none;
    transition: border-color 0.2s;
}

.sort-select:focus {
    border-color: #1976d2;
}

.content-body {
    position: relative;
    min-height: 400px;
}

.content-grid {
    display: grid;
    gap: 16px;
    transition: all 0.3s ease;
}

/* 列表视图 */
.content-grid.list-view {
    grid-template-columns: 1fr;
}

/* 网格视图 */
.content-grid.grid-view {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
}

/* 图表视图 */
.content-grid.chart-view {
    grid-template-columns: 1fr;
}

.content-item {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 16px;
    transition: all 0.2s;
    cursor: pointer;
}

.content-item:hover {
    border-color: #1976d2;
    box-shadow: 0 4px 12px rgba(25,118,210,0.15);
    transform: translateY(-2px);
}

.item-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12px;
}

.item-info {
    flex: 1;
}

.item-name {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0 0 4px 0;
}

.item-code {
    font-size: 12px;
    color: #666;
    background: #f0f0f0;
    padding: 2px 6px;
    border-radius: 4px;
    display: inline-block;
}

.item-price {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    text-align: right;
}

.item-change {
    font-size: 14px;
    font-weight: 500;
    text-align: right;
    margin-top: 4px;
}

.item-change.positive {
    color: #4CAF50;
}

.item-change.negative {
    color: #f44336;
}

.item-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: 12px;
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid #f0f0f0;
}

.detail-item {
    text-align: center;
}

.detail-label {
    font-size: 11px;
    color: #666;
    margin-bottom: 2px;
}

.detail-value {
    font-size: 13px;
    font-weight: 500;
    color: #333;
}

.item-actions {
    display: flex;
    gap: 8px;
    margin-top: 12px;
}

.action-btn {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    background: white;
    color: #666;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s;
}

.action-btn:hover {
    border-color: #1976d2;
    color: #1976d2;
}

.action-btn.primary {
    background: #1976d2;
    color: white;
    border-color: #1976d2;
}

.action-btn.primary:hover {
    background: #1565c0;
}

.loading-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px;
    gap: 16px;
}

.spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #f0f0f0;
    border-top: 3px solid #1976d2;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    text-align: center;
}

.empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.empty-state h3 {
    font-size: 18px;
    color: #333;
    margin: 0 0 8px 0;
}

.empty-state p {
    color: #666;
    margin: 0 0 24px 0;
}

.retry-btn {
    background: #1976d2;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 10px 20px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.retry-btn:hover {
    background: #1565c0;
}

.pagination-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 32px;
    padding-top: 24px;
    border-top: 1px solid #e0e0e0;
    flex-wrap: wrap;
    gap: 16px;
}

.pagination-info {
    font-size: 14px;
    color: #666;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 8px;
}

.page-btn {
    padding: 8px 12px;
    border: 1px solid #e0e0e0;
    background: white;
    color: #666;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s;
    min-width: 40px;
    text-align: center;
}

.page-btn:hover:not(.disabled) {
    border-color: #1976d2;
    color: #1976d2;
}

.page-btn.active {
    background: #1976d2;
    color: white;
    border-color: #1976d2;
}

.page-btn.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.page-ellipsis {
    padding: 8px 4px;
    color: #666;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .content-container {
        padding: 16px;
    }
    
    .list-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .header-controls {
        width: 100%;
        justify-content: space-between;
    }
    
    .content-grid.grid-view {
        grid-template-columns: 1fr;
    }
    
    .item-details {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .pagination-section {
        flex-direction: column;
        text-align: center;
    }
    
    .pagination-controls {
        flex-wrap: wrap;
        justify-content: center;
    }
}

/* 图表视图特殊样式 */
.content-item.chart-item {
    padding: 20px;
}

.chart-container {
    height: 200px;
    background: #f8f9fa;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 12px 0;
    color: #666;
    font-size: 14px;
}
</style>

<script>
let currentView = 'list';
let currentPage = 1;
let itemsPerPage = 20;
let totalItems = 1234;
let currentSort = 'relevance';
let isLoading = false;

// 模拟数据
const mockData = [
    {
        name: '苹果公司',
        code: 'AAPL',
        price: 150.25,
        change: 2.45,
        changePercent: 1.66,
        volume: '45.2M',
        marketCap: '2.4T',
        pe: 28.5,
        type: 'stock'
    },
    {
        name: '特斯拉',
        code: 'TSLA',
        price: 800.50,
        change: -15.25,
        changePercent: -1.87,
        volume: '28.7M',
        marketCap: '800B',
        pe: 45.2,
        type: 'stock'
    },
    {
        name: '标普500指数基金',
        code: 'SPY',
        price: 400.15,
        change: 3.20,
        changePercent: 0.81,
        volume: '15.3M',
        marketCap: '350B',
        pe: 22.1,
        type: 'fund'
    }
];

// 切换视图
function switchView(view) {
    currentView = view;
    
    // 更新按钮状态
    document.querySelectorAll('.view-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    document.querySelector(`[data-view="${view}"]`).classList.add('active');
    
    // 更新网格样式
    const grid = document.getElementById('contentGrid');
    grid.className = `content-grid ${view}-view`;
    
    // 重新渲染内容
    renderContent();
}

// 排序结果
function sortResults() {
    const sortBy = document.getElementById('sortBy').value;
    currentSort = sortBy;
    
    console.log('按以下方式排序:', sortBy);
    // 这里可以重新请求数据或对现有数据进行排序
    renderContent();
}

// 渲染内容
function renderContent() {
    const grid = document.getElementById('contentGrid');
    const loadingIndicator = document.getElementById('loadingIndicator');
    const emptyState = document.getElementById('emptyState');
    
    // 显示加载状态
    showLoading();
    
    // 模拟加载延迟
    setTimeout(() => {
        if (mockData.length === 0) {
            showEmptyState();
        } else {
            hideLoading();
            hideEmptyState();
            
            grid.innerHTML = mockData.map(item => createItemHTML(item)).join('');
        }
        
        updatePagination();
    }, 500);
}

// 创建项目HTML
function createItemHTML(item) {
    const changeClass = item.change >= 0 ? 'positive' : 'negative';
    const changeSign = item.change >= 0 ? '+' : '';
    
    if (currentView === 'chart') {
        return `
            <div class="content-item chart-item" onclick="viewDetails('${item.code}')">
                <div class="item-header">
                    <div class="item-info">
                        <h3 class="item-name">${item.name}</h3>
                        <span class="item-code">${item.code}</span>
                    </div>
                    <div>
                        <div class="item-price">$${item.price}</div>
                        <div class="item-change ${changeClass}">
                            ${changeSign}${item.change} (${changeSign}${item.changePercent}%)
                        </div>
                    </div>
                </div>
                <div class="chart-container">
                    📈 图表区域 (${item.code})
                </div>
                <div class="item-actions">
                    <button class="action-btn" onclick="addToWatchlist('${item.code}', event)">关注</button>
                    <button class="action-btn primary" onclick="quickTrade('${item.code}', event)">交易</button>
                </div>
            </div>
        `;
    }
    
    return `
        <div class="content-item" onclick="viewDetails('${item.code}')">
            <div class="item-header">
                <div class="item-info">
                    <h3 class="item-name">${item.name}</h3>
                    <span class="item-code">${item.code}</span>
                </div>
                <div>
                    <div class="item-price">$${item.price}</div>
                    <div class="item-change ${changeClass}">
                        ${changeSign}${item.change} (${changeSign}${item.changePercent}%)
                    </div>
                </div>
            </div>
            <div class="item-details">
                <div class="detail-item">
                    <div class="detail-label">成交量</div>
                    <div class="detail-value">${item.volume}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">市值</div>
                    <div class="detail-value">${item.marketCap}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">市盈率</div>
                    <div class="detail-value">${item.pe}</div>
                </div>
            </div>
            <div class="item-actions">
                <button class="action-btn" onclick="addToWatchlist('${item.code}', event)">关注</button>
                <button class="action-btn" onclick="compareItem('${item.code}', event)">对比</button>
                <button class="action-btn primary" onclick="quickTrade('${item.code}', event)">交易</button>
            </div>
        </div>
    `;
}

// 显示加载状态
function showLoading() {
    document.getElementById('loadingIndicator').style.display = 'flex';
    document.getElementById('contentGrid').style.display = 'none';
    isLoading = true;
}

// 隐藏加载状态
function hideLoading() {
    document.getElementById('loadingIndicator').style.display = 'none';
    document.getElementById('contentGrid').style.display = 'grid';
    isLoading = false;
}

// 显示空状态
function showEmptyState() {
    document.getElementById('emptyState').style.display = 'flex';
    document.getElementById('contentGrid').style.display = 'none';
    hideLoading();
}

// 隐藏空状态
function hideEmptyState() {
    document.getElementById('emptyState').style.display = 'none';
}

// 重新加载
function retryLoad() {
    renderContent();
}

// 更新分页
function updatePagination() {
    const totalPages = Math.ceil(totalItems / itemsPerPage);
    const startItem = (currentPage - 1) * itemsPerPage + 1;
    const endItem = Math.min(currentPage * itemsPerPage, totalItems);
    
    // 更新信息显示
    document.getElementById('currentRange').textContent = `${startItem}-${endItem}`;
    document.getElementById('totalItems').textContent = totalItems.toLocaleString();
    document.getElementById('resultCount').textContent = `共找到 ${totalItems.toLocaleString()} 个结果`;
    
    // 生成分页控件
    const paginationControls = document.getElementById('paginationControls');
    paginationControls.innerHTML = generatePaginationHTML(currentPage, totalPages);
}

// 生成分页HTML
function generatePaginationHTML(current, total) {
    let html = '';
    
    // 上一页按钮
    html += `<button class="page-btn ${current <= 1 ? 'disabled' : ''}" 
             onclick="goToPage(${current - 1})" ${current <= 1 ? 'disabled' : ''}>
             ‹ 上一页
             </button>`;
    
    // 页码按钮
    const startPage = Math.max(1, current - 2);
    const endPage = Math.min(total, current + 2);
    
    if (startPage > 1) {
        html += `<button class="page-btn" onclick="goToPage(1)">1</button>`;
        if (startPage > 2) {
            html += `<span class="page-ellipsis">...</span>`;
        }
    }
    
    for (let i = startPage; i <= endPage; i++) {
        html += `<button class="page-btn ${i === current ? 'active' : ''}" 
                 onclick="goToPage(${i})">${i}</button>`;
    }
    
    if (endPage < total) {
        if (endPage < total - 1) {
            html += `<span class="page-ellipsis">...</span>`;
        }
        html += `<button class="page-btn" onclick="goToPage(${total})">${total}</button>`;
    }
    
    // 下一页按钮
    html += `<button class="page-btn ${current >= total ? 'disabled' : ''}" 
             onclick="goToPage(${current + 1})" ${current >= total ? 'disabled' : ''}>
             下一页 ›
             </button>`;
    
    return html;
}

// 跳转到指定页面
function goToPage(page) {
    if (page < 1 || page > Math.ceil(totalItems / itemsPerPage) || isLoading) {
        return;
    }
    
    currentPage = page;
    renderContent();
}

// 查看详情
function viewDetails(code) {
    console.log('查看详情:', code);
    // 这里可以跳转到详情页面或打开模态框
}

// 添加到关注列表
function addToWatchlist(code, event) {
    event.stopPropagation();
    console.log('添加到关注列表:', code);
    // 这里可以调用API添加到关注列表
}

// 对比项目
function compareItem(code, event) {
    event.stopPropagation();
    console.log('对比项目:', code);
    // 这里可以添加到对比列表
}

// 快速交易
function quickTrade(code, event) {
    event.stopPropagation();
    console.log('快速交易:', code);
    // 这里可以打开交易面板
}

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    renderContent();
});
</script>
