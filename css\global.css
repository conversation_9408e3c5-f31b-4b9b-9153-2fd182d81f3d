/* 全局样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Seguro UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f7fa;
}

/* 主要布局容器 */
.main-container {
    display: flex;
    min-height: 100vh;
}

.sidebar-container {
    width: 280px;
    flex-shrink: 0;
    background: white;
    border-right: 1px solid #e0e0e0;
    position: fixed;
    height: 100vh;
    left: 0;
    top: 0;
    z-index: 100;
    overflow-y: auto;
    transition: transform 0.3s ease;
}

.main-content {
    flex: 1;
    margin-left: 280px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.content-wrapper {
    flex: 1;
    padding: 0 0 40px 0;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 通用工具类 */
.hide {
    display: none !important;
}

.show {
    display: block !important;
}

.loading {
    opacity: 0.6;
    pointer-events: none;
}

.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

.backdrop-blur {
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

/* 无障碍样式 */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* 焦点样式 */
button:focus,
input:focus,
select:focus,
a:focus {
    outline: 2px solid #1976d2;
    outline-offset: 2px;
}

/* 消息提示样式 */
.error-message {
    color: #f44336;
    background: #ffebee;
    padding: 12px;
    border-radius: 6px;
    margin: 12px 0;
    border: 1px solid #ffcdd2;
}

.success-message {
    color: #4caf50;
    background: #e8f5e8;
    padding: 12px;
    border-radius: 6px;
    margin: 12px 0;
    border: 1px solid #c8e6c9;
}

/* 动画定义 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-12px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideUp {
    from {
        transform: translateY(0);
        opacity: 1;
    }
    to {
        transform: translateY(-100%);
        opacity: 0;
    }
}

/* 移动端样式 */
@media (max-width: 768px) {
    .sidebar-container {
        transform: translateX(-100%);
        z-index: 1000;
    }
    
    .sidebar-container.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .mobile-menu-btn {
        position: fixed;
        top: 20px;
        left: 20px;
        z-index: 1001;
        background: #1976d2;
        color: white;
        border: none;
        border-radius: 8px;
        padding: 12px;
        cursor: pointer;
        font-size: 18px;
        box-shadow: 0 2px 8px rgba(25,118,210,0.3);
    }
    
    .mobile-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0,0,0,0.5);
        z-index: 999;
        display: none;
    }
    
    .mobile-overlay.show {
        display: block;
    }
}

/* 响应式容器 */
@media (max-width: 1200px) {
    .container {
        padding: 0 16px;
    }
}
