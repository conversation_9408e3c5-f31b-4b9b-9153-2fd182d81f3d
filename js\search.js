// 搜索模块
class SearchModule {
    constructor() {
        this.currentCategory = 'all';
        this.searchTimeout = null;
        this.advancedFiltersOpen = false;
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadSearchHistory();
    }

    setupEventListeners() {
        // 搜索输入框事件
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => this.handleSearchInput(e));
            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.performSearch();
                }
            });
        }

        // 点击外部关闭搜索建议
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.search-form')) {
                this.hideSuggestions();
            }
        });

        // 分类标签点击
        document.addEventListener('click', (e) => {
            if (e.target.matches('.filter-tab')) {
                this.handleCategoryClick(e);
            }
        });

        // 快速筛选标签点击
        document.addEventListener('click', (e) => {
            if (e.target.matches('.quick-filter-tag')) {
                this.handleQuickFilterClick(e);
            }
        });
    }

    // 处理搜索输入
    handleSearchInput(e) {
        const query = e.target.value.trim();
        
        // 清除之前的定时器
        if (this.searchTimeout) {
            clearTimeout(this.searchTimeout);
        }
        
        if (query.length >= 2) {
            // 延迟搜索，避免频繁请求
            this.searchTimeout = setTimeout(() => {
                this.showSearchSuggestions(query);
            }, 300);
        } else {
            this.hideSuggestions();
        }
    }

    // 执行搜索
    performSearch() {
        const searchInput = document.getElementById('searchInput');
        const query = searchInput ? searchInput.value.trim() : '';
        
        if (query) {
            console.log('执行搜索:', query);
            this.addToSearchHistory(query);
            this.hideSuggestions();
            
            // 触发内容列表更新
            if (window.contentModule) {
                window.contentModule.updateContent({ query, category: this.currentCategory });
            }
            
            if (window.tradeApp) {
                window.tradeApp.showSuccessMessage(`搜索"${query}"完成`);
            }
        }
    }

    // 显示搜索建议
    async showSearchSuggestions(query) {
        const suggestions = document.getElementById('searchSuggestions');
        if (!suggestions) return;

        try {
            const suggestionData = await this.fetchSearchSuggestions(query);
            
            if (suggestionData.length > 0) {
                suggestions.innerHTML = suggestionData.map(item => `
                    <div class="suggestion-item" onclick="window.searchModule.selectSuggestion('${item.name}')">
                        <div class="suggestion-info">
                            <span class="suggestion-name">${item.name}</span>
                            <span class="suggestion-type">${item.type}</span>
                        </div>
                        <span class="suggestion-price">${item.price}</span>
                    </div>
                `).join('');
                
                suggestions.classList.add('show');
            } else {
                this.hideSuggestions();
            }
        } catch (error) {
            console.error('Failed to load search suggestions:', error);
            this.hideSuggestions();
        }
    }

    // 获取搜索建议
    async fetchSearchSuggestions(query) {
        return new Promise(resolve => {
            setTimeout(() => {
                // 模拟搜索建议数据
                const mockSuggestions = [
                    { name: '苹果公司 (AAPL)', type: '股票', price: '$150.25' },
                    { name: '特斯拉 (TSLA)', type: '股票', price: '$800.50' },
                    { name: '标普500指数基金', type: '基金', price: '$400.15' },
                    { name: '黄金期货', type: '期货', price: '$1950.00' },
                    { name: '比特币 (BTC)', type: '数字货币', price: '$45000' }
                ];
                
                const filtered = mockSuggestions.filter(item => 
                    item.name.toLowerCase().includes(query.toLowerCase())
                );
                
                resolve(filtered);
            }, 200);
        });
    }

    // 选择搜索建议
    selectSuggestion(name) {
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.value = name;
        }
        this.hideSuggestions();
        this.performSearch();
    }

    // 隐藏搜索建议
    hideSuggestions() {
        const suggestions = document.getElementById('searchSuggestions');
        if (suggestions) {
            suggestions.classList.remove('show');
        }
    }

    // 语音搜索
    startVoiceSearch() {
        if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            const recognition = new SpeechRecognition();
            
            recognition.lang = 'zh-CN';
            recognition.onresult = (event) => {
                const result = event.results[0][0].transcript;
                const searchInput = document.getElementById('searchInput');
                if (searchInput) {
                    searchInput.value = result;
                }
                this.performSearch();
            };
            
            recognition.onerror = (event) => {
                console.error('Speech recognition error:', event.error);
                if (window.tradeApp) {
                    window.tradeApp.showErrorMessage('语音识别失败，请重试');
                }
            };
            
            recognition.start();
            
            if (window.tradeApp) {
                window.tradeApp.showSuccessMessage('请开始说话...');
            }
        } else {
            if (window.tradeApp) {
                window.tradeApp.showErrorMessage('您的浏览器不支持语音搜索功能');
            }
        }
    }

    // 处理分类点击
    handleCategoryClick(e) {
        const category = e.target.dataset.category;
        this.switchCategory(category);
    }

    // 切换分类
    switchCategory(category) {
        this.currentCategory = category;
        
        // 更新tab状态
        document.querySelectorAll('.filter-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        
        const activeTab = document.querySelector(`[data-category="${category}"]`);
        if (activeTab) {
            activeTab.classList.add('active');
        }
        
        console.log('切换到分类:', category);
        
        // 触发内容更新
        if (window.contentModule) {
            window.contentModule.updateContent({ category });
        }
    }

    // 切换高级筛选
    toggleAdvancedFilters() {
        const toggle = document.querySelector('.filter-toggle');
        const panel = document.getElementById('advancedFilters');
        
        if (toggle && panel) {
            this.advancedFiltersOpen = !this.advancedFiltersOpen;
            toggle.setAttribute('aria-expanded', this.advancedFiltersOpen);
            panel.classList.toggle('show', this.advancedFiltersOpen);
            
            // 如果是首次打开，加载高级筛选内容
            if (this.advancedFiltersOpen && !panel.hasChildNodes()) {
                this.loadAdvancedFilters();
            }
        }
    }

    // 加载高级筛选内容
    loadAdvancedFilters() {
        const panel = document.getElementById('advancedFilters');
        if (panel) {
            panel.innerHTML = `
                <div class="filter-row">
                    <div class="filter-group">
                        <label for="priceRange">价格区间</label>
                        <div class="range-input">
                            <input type="number" id="minPrice" placeholder="最低价" min="0">
                            <span>-</span>
                            <input type="number" id="maxPrice" placeholder="最高价" min="0">
                        </div>
                    </div>
                    <div class="filter-group">
                        <label for="marketCap">市值</label>
                        <select id="marketCap" class="filter-select">
                            <option value="">不限</option>
                            <option value="large">大盘股</option>
                            <option value="mid">中盘股</option>
                            <option value="small">小盘股</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label for="sector">行业</label>
                        <select id="sector" class="filter-select">
                            <option value="">不限</option>
                            <option value="tech">科技</option>
                            <option value="finance">金融</option>
                            <option value="healthcare">医疗</option>
                        </select>
                    </div>
                </div>
                <div class="filter-actions">
                    <button type="button" class="reset-filters" onclick="window.searchModule.resetFilters()">重置筛选</button>
                    <button type="button" class="apply-filters" onclick="window.searchModule.applyFilters()">应用筛选</button>
                </div>
            `;
        }
    }

    // 重置筛选条件
    resetFilters() {
        const inputs = document.querySelectorAll('#advancedFilters input, #advancedFilters select');
        inputs.forEach(input => {
            input.value = '';
        });
        
        console.log('已重置筛选条件');
        if (window.tradeApp) {
            window.tradeApp.showSuccessMessage('筛选条件已重置');
        }
    }

    // 应用筛选条件
    applyFilters() {
        const filters = {
            minPrice: document.getElementById('minPrice')?.value || '',
            maxPrice: document.getElementById('maxPrice')?.value || '',
            marketCap: document.getElementById('marketCap')?.value || '',
            sector: document.getElementById('sector')?.value || '',
            category: this.currentCategory
        };
        
        console.log('应用筛选条件:', filters);
        
        // 触发内容更新
        if (window.contentModule) {
            window.contentModule.updateContent({ filters });
        }
        
        // 关闭高级筛选面板
        this.toggleAdvancedFilters();
        
        if (window.tradeApp) {
            window.tradeApp.showSuccessMessage('筛选条件已应用');
        }
    }

    // 处理快速筛选点击
    handleQuickFilterClick(e) {
        const filterName = e.target.textContent;
        this.applyQuickFilter(filterName);
    }

    // 快速筛选
    applyQuickFilter(filterName) {
        console.log('应用快速筛选:', filterName);
        
        // 根据快速筛选名称设置对应的筛选条件
        const quickFilterMap = {
            '热门股票': { category: 'stocks', sort: 'popularity' },
            '新股上市': { category: 'stocks', filter: 'new_listing' },
            '高分红': { category: 'stocks', filter: 'high_dividend' },
            '低估值': { category: 'stocks', filter: 'undervalued' },
            '成长股': { category: 'stocks', filter: 'growth' }
        };
        
        const filterConfig = quickFilterMap[filterName];
        if (filterConfig) {
            // 更新分类
            if (filterConfig.category) {
                this.switchCategory(filterConfig.category);
            }
            
            // 触发内容更新
            if (window.contentModule) {
                window.contentModule.updateContent(filterConfig);
            }
        }
        
        if (window.tradeApp) {
            window.tradeApp.showSuccessMessage(`已应用"${filterName}"筛选`);
        }
    }

    // 搜索历史管理
    loadSearchHistory() {
        const history = localStorage.getItem('searchHistory');
        this.searchHistory = history ? JSON.parse(history) : [];
    }

    addToSearchHistory(query) {
        // 避免重复
        this.searchHistory = this.searchHistory.filter(item => item !== query);
        this.searchHistory.unshift(query);
        
        // 只保留最近20个
        this.searchHistory = this.searchHistory.slice(0, 20);
        
        localStorage.setItem('searchHistory', JSON.stringify(this.searchHistory));
    }

    getSearchHistory() {
        return this.searchHistory || [];
    }
}

// 导出模块供全局使用
window.SearchModule = SearchModule;

// 将方法暴露到全局对象
if (window.tradeApp) {
    window.tradeApp.performSearch = function() {
        if (window.searchModule) {
            window.searchModule.performSearch();
        }
    };

    window.tradeApp.startVoiceSearch = function() {
        if (window.searchModule) {
            window.searchModule.startVoiceSearch();
        }
    };

    window.tradeApp.switchCategory = function(category) {
        if (window.searchModule) {
            window.searchModule.switchCategory(category);
        }
    };

    window.tradeApp.toggleAdvancedFilters = function() {
        if (window.searchModule) {
            window.searchModule.toggleAdvancedFilters();
        }
    };

    window.tradeApp.applyQuickFilter = function(filterName) {
        if (window.searchModule) {
            window.searchModule.applyQuickFilter(filterName);
        }
    };
}

// 初始化搜索模块
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.searchModule = new SearchModule();
    });
} else {
    window.searchModule = new SearchModule();
}
