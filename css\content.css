/* 内容列表样式 */
.content-list {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    margin-bottom: 24px;
    overflow: hidden;
}

.content-container {
    padding: 24px;
}

.list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    flex-wrap: wrap;
    gap: 16px;
}

.header-left h2 {
    font-size: 24px;
    font-weight: 600;
    color: #333;
    margin: 0 0 4px 0;
}

.result-count {
    font-size: 14px;
    color: #666;
}

.header-controls {
    display: flex;
    align-items: center;
    gap: 16px;
}

.view-controls {
    display: flex;
    background: #f5f5f5;
    border-radius: 8px;
    padding: 4px;
}

.view-btn {
    background: none;
    border: none;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 16px;
    transition: all 0.2s;
    color: #666;
}

.view-btn:hover,
.view-btn.active {
    background: white;
    color: #1976d2;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.sort-select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background: white;
    cursor: pointer;
    outline: none;
    transition: border-color 0.2s;
}

.sort-select:focus {
    border-color: #1976d2;
}

.content-body {
    position: relative;
    min-height: 400px;
}

.content-grid {
    display: grid;
    gap: 16px;
    transition: all 0.3s ease;
}

/* 列表视图 */
.content-grid.list-view {
    grid-template-columns: 1fr;
}

/* 网格视图 */
.content-grid.grid-view {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
}

/* 图表视图 */
.content-grid.chart-view {
    grid-template-columns: 1fr;
}

.content-item {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 16px;
    transition: all 0.2s;
    cursor: pointer;
}

.content-item:hover {
    border-color: #1976d2;
    box-shadow: 0 4px 12px rgba(25,118,210,0.15);
    transform: translateY(-2px);
}

.item-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12px;
}

.item-info {
    flex: 1;
}

.item-name {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0 0 4px 0;
}

.item-code {
    font-size: 12px;
    color: #666;
    background: #f0f0f0;
    padding: 2px 6px;
    border-radius: 4px;
    display: inline-block;
}

.item-price {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    text-align: right;
}

.item-change {
    font-size: 14px;
    font-weight: 500;
    text-align: right;
    margin-top: 4px;
}

.item-change.positive {
    color: #4CAF50;
}

.item-change.negative {
    color: #f44336;
}

.item-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: 12px;
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid #f0f0f0;
}

.detail-item {
    text-align: center;
}

.detail-label {
    font-size: 11px;
    color: #666;
    margin-bottom: 2px;
}

.detail-value {
    font-size: 13px;
    font-weight: 500;
    color: #333;
}

.item-actions {
    display: flex;
    gap: 8px;
    margin-top: 12px;
}

.action-btn {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    background: white;
    color: #666;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s;
}

.action-btn:hover {
    border-color: #1976d2;
    color: #1976d2;
}

.action-btn.primary {
    background: #1976d2;
    color: white;
    border-color: #1976d2;
}

.action-btn.primary:hover {
    background: #1565c0;
}

.loading-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px;
    gap: 16px;
}

.spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #f0f0f0;
    border-top: 3px solid #1976d2;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    text-align: center;
}

.empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.empty-state h3 {
    font-size: 18px;
    color: #333;
    margin: 0 0 8px 0;
}

.empty-state p {
    color: #666;
    margin: 0 0 24px 0;
}

.retry-btn {
    background: #1976d2;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 10px 20px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.retry-btn:hover {
    background: #1565c0;
}

.pagination-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 32px;
    padding-top: 24px;
    border-top: 1px solid #e0e0e0;
    flex-wrap: wrap;
    gap: 16px;
}

.pagination-info {
    font-size: 14px;
    color: #666;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 8px;
}

.page-btn {
    padding: 8px 12px;
    border: 1px solid #e0e0e0;
    background: white;
    color: #666;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s;
    min-width: 40px;
    text-align: center;
}

.page-btn:hover:not(.disabled) {
    border-color: #1976d2;
    color: #1976d2;
}

.page-btn.active {
    background: #1976d2;
    color: white;
    border-color: #1976d2;
}

.page-btn.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.page-ellipsis {
    padding: 8px 4px;
    color: #666;
}

/* 图表视图特殊样式 */
.content-item.chart-item {
    padding: 20px;
}

.chart-container {
    height: 200px;
    background: #f8f9fa;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 12px 0;
    color: #666;
    font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .content-container {
        padding: 16px;
    }
    
    .list-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .header-controls {
        width: 100%;
        justify-content: space-between;
    }
    
    .content-grid.grid-view {
        grid-template-columns: 1fr;
    }
    
    .item-details {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .pagination-section {
        flex-direction: column;
        text-align: center;
    }
    
    .pagination-controls {
        flex-wrap: wrap;
        justify-content: center;
    }
}
