<!-- 搜索和筛选模块 -->
<div class="search-section">
    <div class="search-container">
        <div class="search-header">
            <h2>发现优质交易机会</h2>
            <p>使用智能筛选找到最适合的投资产品</p>
        </div>
        
        <div class="search-form">
            <div class="search-input-group">
                <div class="search-input-wrapper">
                    <span class="search-icon">🔍</span>
                    <input 
                        type="text" 
                        id="searchInput" 
                        placeholder="搜索股票、基金、期货..." 
                        class="search-input"
                        autocomplete="off"
                    >
                    <button type="button" class="voice-search" onclick="startVoiceSearch()" aria-label="语音搜索">
                        🎤
                    </button>
                </div>
                <button type="button" class="search-btn" onclick="performSearch()">搜索</button>
            </div>
            
            <div class="search-suggestions" id="searchSuggestions">
                <!-- 搜索建议将动态加载到这里 -->
            </div>
        </div>
        
        <div class="filter-section">
            <div class="filter-tabs">
                <button class="filter-tab active" data-category="all" onclick="switchCategory('all')">
                    全部
                </button>
                <button class="filter-tab" data-category="stocks" onclick="switchCategory('stocks')">
                    股票
                </button>
                <button class="filter-tab" data-category="funds" onclick="switchCategory('funds')">
                    基金
                </button>
                <button class="filter-tab" data-category="futures" onclick="switchCategory('futures')">
                    期货
                </button>
                <button class="filter-tab" data-category="crypto" onclick="switchCategory('crypto')">
                    数字货币
                </button>
            </div>
            
            <div class="advanced-filters">
                <button class="filter-toggle" onclick="toggleAdvancedFilters()" aria-expanded="false">
                    高级筛选
                    <span class="toggle-icon">▼</span>
                </button>
                
                <div class="filter-panel" id="advancedFilters">
                    <div class="filter-row">
                        <div class="filter-group">
                            <label for="priceRange">价格区间</label>
                            <div class="range-input">
                                <input type="number" id="minPrice" placeholder="最低价" min="0">
                                <span>-</span>
                                <input type="number" id="maxPrice" placeholder="最高价" min="0">
                            </div>
                        </div>
                        
                        <div class="filter-group">
                            <label for="marketCap">市值</label>
                            <select id="marketCap" class="filter-select">
                                <option value="">不限</option>
                                <option value="large">大盘股</option>
                                <option value="mid">中盘股</option>
                                <option value="small">小盘股</option>
                            </select>
                        </div>
                        
                        <div class="filter-group">
                            <label for="sector">行业</label>
                            <select id="sector" class="filter-select">
                                <option value="">不限</option>
                                <option value="tech">科技</option>
                                <option value="finance">金融</option>
                                <option value="healthcare">医疗</option>
                                <option value="energy">能源</option>
                                <option value="consumer">消费</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="filter-row">
                        <div class="filter-group">
                            <label for="performance">涨跌幅</label>
                            <select id="performance" class="filter-select">
                                <option value="">不限</option>
                                <option value="up5">上涨>5%</option>
                                <option value="up1">上涨>1%</option>
                                <option value="down1">下跌>1%</option>
                                <option value="down5">下跌>5%</option>
                            </select>
                        </div>
                        
                        <div class="filter-group">
                            <label for="volume">成交量</label>
                            <select id="volume" class="filter-select">
                                <option value="">不限</option>
                                <option value="high">放量</option>
                                <option value="low">缩量</option>
                            </select>
                        </div>
                        
                        <div class="filter-group">
                            <label for="rating">评级</label>
                            <select id="rating" class="filter-select">
                                <option value="">不限</option>
                                <option value="buy">买入</option>
                                <option value="hold">持有</option>
                                <option value="sell">卖出</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="filter-actions">
                        <button type="button" class="reset-filters" onclick="resetFilters()">
                            重置筛选
                        </button>
                        <button type="button" class="apply-filters" onclick="applyFilters()">
                            应用筛选
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="quick-filters">
            <span class="quick-filter-label">热门筛选：</span>
            <div class="quick-filter-tags">
                <button class="quick-filter-tag" onclick="applyQuickFilter('热门股票')">热门股票</button>
                <button class="quick-filter-tag" onclick="applyQuickFilter('新股上市')">新股上市</button>
                <button class="quick-filter-tag" onclick="applyQuickFilter('高分红')">高分红</button>
                <button class="quick-filter-tag" onclick="applyQuickFilter('低估值')">低估值</button>
                <button class="quick-filter-tag" onclick="applyQuickFilter('成长股')">成长股</button>
            </div>
        </div>
    </div>
</div>

<style>
.search-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 40px 20px;
    margin-bottom: 24px;
}

.search-container {
    max-width: 1200px;
    margin: 0 auto;
}

.search-header {
    text-align: center;
    margin-bottom: 32px;
}

.search-header h2 {
    font-size: 28px;
    font-weight: 600;
    margin: 0 0 8px 0;
}

.search-header p {
    font-size: 16px;
    opacity: 0.9;
    margin: 0;
}

.search-form {
    position: relative;
    margin-bottom: 32px;
}

.search-input-group {
    display: flex;
    gap: 12px;
    max-width: 600px;
    margin: 0 auto;
}

.search-input-wrapper {
    flex: 1;
    position: relative;
    display: flex;
    align-items: center;
    background: white;
    border-radius: 12px;
    padding: 0 16px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.search-icon {
    font-size: 18px;
    color: #666;
    margin-right: 12px;
}

.search-input {
    flex: 1;
    border: none;
    outline: none;
    padding: 16px 0;
    font-size: 16px;
    color: #333;
    background: transparent;
}

.search-input::placeholder {
    color: #999;
}

.voice-search {
    background: none;
    border: none;
    font-size: 18px;
    color: #666;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: background-color 0.2s;
}

.voice-search:hover {
    background-color: #f5f5f5;
}

.search-btn {
    background: #4CAF50;
    color: white;
    border: none;
    border-radius: 12px;
    padding: 16px 24px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    box-shadow: 0 4px 12px rgba(76,175,80,0.3);
}

.search-btn:hover {
    background: #45a049;
    transform: translateY(-1px);
    box-shadow: 0 6px 16px rgba(76,175,80,0.4);
}

.search-suggestions {
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    width: 100%;
    max-width: 600px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 24px rgba(0,0,0,0.15);
    display: none;
    z-index: 1000;
    margin-top: 4px;
}

.search-suggestions.show {
    display: block;
    animation: fadeIn 0.2s ease-out;
}

.filter-section {
    margin-bottom: 24px;
}

.filter-tabs {
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-bottom: 24px;
    flex-wrap: wrap;
}

.filter-tab {
    background: rgba(255,255,255,0.2);
    color: white;
    border: 1px solid rgba(255,255,255,0.3);
    border-radius: 24px;
    padding: 8px 20px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

.filter-tab:hover,
.filter-tab.active {
    background: white;
    color: #667eea;
    border-color: white;
}

.advanced-filters {
    text-align: center;
}

.filter-toggle {
    background: rgba(255,255,255,0.2);
    color: white;
    border: 1px solid rgba(255,255,255,0.3);
    border-radius: 8px;
    padding: 12px 20px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0 auto;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

.filter-toggle:hover {
    background: rgba(255,255,255,0.3);
}

.toggle-icon {
    transition: transform 0.2s;
}

.filter-toggle[aria-expanded="true"] .toggle-icon {
    transform: rotate(180deg);
}

.filter-panel {
    background: rgba(255,255,255,0.95);
    color: #333;
    border-radius: 12px;
    padding: 24px;
    margin-top: 16px;
    display: none;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

.filter-panel.show {
    display: block;
    animation: slideDown 0.3s ease-out;
}

.filter-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.filter-group label {
    font-size: 14px;
    font-weight: 500;
    color: #555;
}

.filter-select,
.range-input input {
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    outline: none;
    transition: border-color 0.2s;
}

.filter-select:focus,
.range-input input:focus {
    border-color: #667eea;
}

.range-input {
    display: flex;
    align-items: center;
    gap: 8px;
}

.range-input input {
    flex: 1;
}

.filter-actions {
    display: flex;
    justify-content: center;
    gap: 12px;
    margin-top: 24px;
}

.reset-filters,
.apply-filters {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s;
}

.reset-filters {
    background: #f5f5f5;
    color: #666;
}

.reset-filters:hover {
    background: #e0e0e0;
}

.apply-filters {
    background: #4CAF50;
    color: white;
}

.apply-filters:hover {
    background: #45a049;
}

.quick-filters {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    flex-wrap: wrap;
}

.quick-filter-label {
    font-size: 14px;
    opacity: 0.9;
    white-space: nowrap;
}

.quick-filter-tags {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.quick-filter-tag {
    background: rgba(255,255,255,0.2);
    color: white;
    border: 1px solid rgba(255,255,255,0.3);
    border-radius: 16px;
    padding: 6px 14px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

.quick-filter-tag:hover {
    background: rgba(255,255,255,0.3);
    transform: translateY(-1px);
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-12px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .search-section {
        padding: 24px 16px;
    }
    
    .search-header h2 {
        font-size: 24px;
    }
    
    .search-input-group {
        flex-direction: column;
    }
    
    .filter-tabs {
        justify-content: flex-start;
        overflow-x: auto;
        padding-bottom: 8px;
    }
    
    .filter-row {
        grid-template-columns: 1fr;
    }
    
    .quick-filters {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .quick-filter-tags {
        width: 100%;
    }
}
</style>

<script>
let currentCategory = 'all';
let searchTimeout = null;

// 搜索功能
function performSearch() {
    const searchInput = document.getElementById('searchInput');
    const query = searchInput.value.trim();
    
    if (query) {
        console.log('执行搜索:', query);
        // 这里可以调用搜索API
        // 实际项目中会发送请求到后端
        hideSuggestions();
    }
}

// 实时搜索建议
document.getElementById('searchInput')?.addEventListener('input', function(e) {
    const query = e.target.value.trim();
    
    // 清除之前的定时器
    if (searchTimeout) {
        clearTimeout(searchTimeout);
    }
    
    if (query.length >= 2) {
        // 延迟搜索，避免频繁请求
        searchTimeout = setTimeout(() => {
            showSearchSuggestions(query);
        }, 300);
    } else {
        hideSuggestions();
    }
});

// 显示搜索建议
function showSearchSuggestions(query) {
    const suggestions = document.getElementById('searchSuggestions');
    
    // 模拟搜索建议数据
    const mockSuggestions = [
        { name: '苹果公司 (AAPL)', type: '股票', price: '$150.25' },
        { name: '特斯拉 (TSLA)', type: '股票', price: '$800.50' },
        { name: '标普500指数基金', type: '基金', price: '$400.15' },
        { name: '黄金期货', type: '期货', price: '$1950.00' },
        { name: '比特币 (BTC)', type: '数字货币', price: '$45000' }
    ];
    
    const filteredSuggestions = mockSuggestions.filter(item => 
        item.name.toLowerCase().includes(query.toLowerCase())
    );
    
    if (filteredSuggestions.length > 0) {
        suggestions.innerHTML = filteredSuggestions.map(item => `
            <div class="suggestion-item" onclick="selectSuggestion('${item.name}')">
                <div class="suggestion-info">
                    <span class="suggestion-name">${item.name}</span>
                    <span class="suggestion-type">${item.type}</span>
                </div>
                <span class="suggestion-price">${item.price}</span>
            </div>
        `).join('');
        
        suggestions.classList.add('show');
    } else {
        hideSuggestions();
    }
}

// 选择搜索建议
function selectSuggestion(name) {
    document.getElementById('searchInput').value = name;
    hideSuggestions();
    performSearch();
}

// 隐藏搜索建议
function hideSuggestions() {
    const suggestions = document.getElementById('searchSuggestions');
    suggestions.classList.remove('show');
}

// 语音搜索
function startVoiceSearch() {
    if ('webkitSpeechRecognition' in window) {
        const recognition = new webkitSpeechRecognition();
        recognition.lang = 'zh-CN';
        recognition.onresult = function(event) {
            const result = event.results[0][0].transcript;
            document.getElementById('searchInput').value = result;
            performSearch();
        };
        recognition.start();
    } else {
        alert('您的浏览器不支持语音搜索功能');
    }
}

// 切换分类
function switchCategory(category) {
    currentCategory = category;
    
    // 更新tab状态
    document.querySelectorAll('.filter-tab').forEach(tab => {
        tab.classList.remove('active');
    });
    document.querySelector(`[data-category="${category}"]`).classList.add('active');
    
    console.log('切换到分类:', category);
    // 这里可以调用API获取对应分类的数据
}

// 切换高级筛选
function toggleAdvancedFilters() {
    const toggle = document.querySelector('.filter-toggle');
    const panel = document.getElementById('advancedFilters');
    const isExpanded = toggle.getAttribute('aria-expanded') === 'true';
    
    toggle.setAttribute('aria-expanded', !isExpanded);
    panel.classList.toggle('show');
}

// 重置筛选条件
function resetFilters() {
    // 重置所有筛选表单
    document.getElementById('minPrice').value = '';
    document.getElementById('maxPrice').value = '';
    document.getElementById('marketCap').value = '';
    document.getElementById('sector').value = '';
    document.getElementById('performance').value = '';
    document.getElementById('volume').value = '';
    document.getElementById('rating').value = '';
    
    console.log('已重置筛选条件');
}

// 应用筛选条件
function applyFilters() {
    const filters = {
        minPrice: document.getElementById('minPrice').value,
        maxPrice: document.getElementById('maxPrice').value,
        marketCap: document.getElementById('marketCap').value,
        sector: document.getElementById('sector').value,
        performance: document.getElementById('performance').value,
        volume: document.getElementById('volume').value,
        rating: document.getElementById('rating').value,
        category: currentCategory
    };
    
    console.log('应用筛选条件:', filters);
    // 这里可以调用API根据筛选条件获取数据
    
    // 关闭高级筛选面板
    toggleAdvancedFilters();
}

// 快速筛选
function applyQuickFilter(filterName) {
    console.log('应用快速筛选:', filterName);
    // 这里可以根据快速筛选名称设置对应的筛选条件
    // 然后调用API获取数据
}

// 点击外部关闭搜索建议
document.addEventListener('click', function(event) {
    const searchForm = document.querySelector('.search-form');
    if (!searchForm.contains(event.target)) {
        hideSuggestions();
    }
});

// 回车执行搜索
document.getElementById('searchInput')?.addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        performSearch();
    }
});

// 添加搜索建议样式
const suggestionStyles = `
    .suggestion-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        cursor: pointer;
        transition: background-color 0.2s;
        border-bottom: 1px solid #f0f0f0;
    }
    
    .suggestion-item:hover {
        background-color: #f8f9fa;
    }
    
    .suggestion-item:last-child {
        border-bottom: none;
    }
    
    .suggestion-info {
        display: flex;
        flex-direction: column;
        gap: 4px;
    }
    
    .suggestion-name {
        font-weight: 500;
        color: #333;
    }
    
    .suggestion-type {
        font-size: 12px;
        color: #666;
        background: #f0f0f0;
        padding: 2px 6px;
        border-radius: 4px;
        width: fit-content;
    }
    
    .suggestion-price {
        font-weight: 600;
        color: #4CAF50;
    }
`;

const styleSheet = document.createElement('style');
styleSheet.textContent = suggestionStyles;
document.head.appendChild(styleSheet);
</script>
