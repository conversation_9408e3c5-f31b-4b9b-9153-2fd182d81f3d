<!DOCTYPE html>
<html lang="zh-Hans" class="h-full" data-kantu="1">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, viewport-fit=cover, user-scalable=no">
    <meta name="theme-color" content="#242525">
    <title>AI风月 - 探索</title>
    
    <!-- SEO Meta Tags -->
    <meta property="og:title" content="AI风月">
    <meta property="og:description" content="AI风月 ｜ 致力于打造全网最好的ai情感交流聊天平台，于2024年8月1日正式上线，以安全，自由，流畅为主打，要给用户最好的交流体验。">
    <meta property="og:locale" content="zh-Hans">
    <meta name="twitter:card" content="summary">
    <meta name="twitter:title" content="AI风月">
    <meta name="twitter:description" content="AI风月 ｜ 致力于打造全网最好的ai情感交流聊天平台，于2024年8月1日正式上线，以安全，自由，流畅为主打，要给用户最好的交流体验。">
    
    <!-- Dark Reader Styles -->
    <style class="darkreader darkreader--inline" media="screen">
        /* Dark Reader inline styles */
    </style>
    
    <!-- Core CSS Variables and Base Styles -->
    <style>
        :root {
            --max-width: 1100px;
            --border-radius: 12px;
            --font-system: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Microsoft YaHei", "微软雅黑", Gantari, Helvetica, Arial, sans-serif;
            --primary-color: #1e88e5;
            --primary-color-light: rgba(28,100,242,0.1);
            --success-color: #3aa66c;
            --error-color: #f44336;
            --warning-color: #f78b00;
            --text-title: #000000;
            --text-primary: #333333;
            --text-secondary: #6b7280;
            --text-disabled: #cccccc;
            --background-main: #f3f4f6;
            --background-primary: #e3f2fd;
            --border-main: #e5e7eb;
        }
        
        * {
            box-sizing: border-box;
            padding: 0;
            margin: 0;
        }
        
        html, body {
            max-width: 100vw;
            overflow: hidden;
            font-family: var(--font-system);
            -webkit-user-select: none;
            -moz-user-select: none;
        }
        
        body {
            color: rgba(0,0,0,0.85);
            background-color: #fff;
        }
        
        .disable-drag {
            -webkit-user-drag: none;
        }
        
        *:focus-visible {
            outline: none;
        }
        
        a {
            color: inherit;
            text-decoration: none;
            outline: none;
        }
        
        button:focus-within {
            outline: none;
        }
        
        .img-mode {
            visibility: visible !important;
        }
    </style>
    
    <!-- Tailwind CSS Classes -->
    <link rel="stylesheet" href="tailwind.css">
    
    <!-- Material-UI Styles -->
    <link rel="stylesheet" href="mui-styles.css">
</head>
<body class="h-full select-auto" data-api-prefix="https://aiporn.tw/console/api" data-pubic-api-prefix="https://aiporn.tw/api">
    <div class="flex flex-col h-full overflow-y-auto">
        <div class="grow relative flex flex-col overflow-y-auto overflow-x-hidden bg-gray-100">
            <div class="flex h-full bg-gray-100 overflow-hidden">
                <!-- Sidebar -->
                <div id="sidebar-container"></div>
                
                <!-- Main Content -->
                <div class="w-0 grow">
                    <!-- Header Banner -->
                    <div id="header-banner"></div>
                    
                    <!-- Search Section -->
                    <div id="search-section"></div>
                    
                    <!-- Content List -->
                    <div id="content-list"></div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Scripts -->
    <script src="main.js"></script>
</body>
</html>
