// 内容列表模块
class ContentModule {
    constructor() {
        this.currentView = 'list';
        this.currentPage = 1;
        this.itemsPerPage = 20;
        this.totalItems = 1234;
        this.currentSort = 'relevance';
        this.isLoading = false;
        this.data = [];
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadInitialData();
    }

    setupEventListeners() {
        // 视图切换按钮
        document.addEventListener('click', (e) => {
            if (e.target.matches('.view-btn')) {
                this.handleViewChange(e);
            }
        });

        // 内容项点击
        document.addEventListener('click', (e) => {
            if (e.target.matches('.content-item') || e.target.closest('.content-item')) {
                this.handleItemClick(e);
            }
        });

        // 操作按钮点击
        document.addEventListener('click', (e) => {
            if (e.target.matches('.action-btn')) {
                this.handleActionClick(e);
            }
        });

        // 分页按钮点击
        document.addEventListener('click', (e) => {
            if (e.target.matches('.page-btn')) {
                this.handlePageClick(e);
            }
        });

        // 排序选择
        const sortSelect = document.getElementById('sortBy');
        if (sortSelect) {
            sortSelect.addEventListener('change', () => this.sortResults());
        }
    }

    // 处理视图切换
    handleViewChange(e) {
        const view = e.target.dataset.view;
        if (view) {
            this.switchView(view);
        }
    }

    // 切换视图
    switchView(view) {
        this.currentView = view;
        
        // 更新按钮状态
        document.querySelectorAll('.view-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        
        const activeBtn = document.querySelector(`[data-view="${view}"]`);
        if (activeBtn) {
            activeBtn.classList.add('active');
        }
        
        // 更新网格样式
        const grid = document.getElementById('contentGrid');
        if (grid) {
            grid.className = `content-grid ${view}-view`;
        }
        
        // 重新渲染内容
        this.renderContent();
    }

    // 排序结果
    sortResults() {
        const sortBy = document.getElementById('sortBy')?.value;
        if (sortBy) {
            this.currentSort = sortBy;
            console.log('按以下方式排序:', sortBy);
            this.updateContent({ sort: sortBy });
        }
    }

    // 加载初始数据
    async loadInitialData() {
        try {
            this.data = await this.fetchData();
            this.renderContent();
            this.updatePagination();
        } catch (error) {
            console.error('Failed to load initial data:', error);
            this.showEmptyState();
        }
    }

    // 获取数据
    async fetchData(params = {}) {
        return new Promise(resolve => {
            setTimeout(() => {
                // 模拟数据
                const mockData = [
                    {
                        name: '苹果公司',
                        code: 'AAPL',
                        price: 150.25,
                        change: 2.45,
                        changePercent: 1.66,
                        volume: '45.2M',
                        marketCap: '2.4T',
                        pe: 28.5,
                        type: 'stock'
                    },
                    {
                        name: '特斯拉',
                        code: 'TSLA',
                        price: 800.50,
                        change: -15.25,
                        changePercent: -1.87,
                        volume: '28.7M',
                        marketCap: '800B',
                        pe: 45.2,
                        type: 'stock'
                    },
                    {
                        name: '标普500指数基金',
                        code: 'SPY',
                        price: 400.15,
                        change: 3.20,
                        changePercent: 0.81,
                        volume: '15.3M',
                        marketCap: '350B',
                        pe: 22.1,
                        type: 'fund'
                    },
                    {
                        name: '微软公司',
                        code: 'MSFT',
                        price: 310.80,
                        change: 5.60,
                        changePercent: 1.84,
                        volume: '32.1M',
                        marketCap: '2.3T',
                        pe: 31.2,
                        type: 'stock'
                    },
                    {
                        name: '亚马逊',
                        code: 'AMZN',
                        price: 3240.90,
                        change: -28.15,
                        changePercent: -0.86,
                        volume: '18.9M',
                        marketCap: '1.6T',
                        pe: 58.4,
                        type: 'stock'
                    }
                ];
                
                resolve(mockData);
            }, 500);
        });
    }

    // 更新内容
    async updateContent(params = {}) {
        console.log('Updating content with params:', params);
        
        this.showLoading();
        
        try {
            this.data = await this.fetchData(params);
            this.renderContent();
            this.updatePagination();
            this.hideLoading();
        } catch (error) {
            console.error('Failed to update content:', error);
            this.showEmptyState();
        }
    }

    // 渲染内容
    renderContent() {
        const grid = document.getElementById('contentGrid');
        if (!grid) return;

        if (this.data.length === 0) {
            this.showEmptyState();
            return;
        }

        this.hideEmptyState();
        grid.innerHTML = this.data.map(item => this.createItemHTML(item)).join('');
    }

    // 创建项目HTML
    createItemHTML(item) {
        const changeClass = item.change >= 0 ? 'positive' : 'negative';
        const changeSign = item.change >= 0 ? '+' : '';
        
        if (this.currentView === 'chart') {
            return this.createChartItemHTML(item, changeClass, changeSign);
        }
        
        return `
            <div class="content-item" data-code="${item.code}">
                <div class="item-header">
                    <div class="item-info">
                        <h3 class="item-name">${item.name}</h3>
                        <span class="item-code">${item.code}</span>
                    </div>
                    <div>
                        <div class="item-price">$${item.price}</div>
                        <div class="item-change ${changeClass}">
                            ${changeSign}${item.change} (${changeSign}${item.changePercent}%)
                        </div>
                    </div>
                </div>
                <div class="item-details">
                    <div class="detail-item">
                        <div class="detail-label">成交量</div>
                        <div class="detail-value">${item.volume}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">市值</div>
                        <div class="detail-value">${item.marketCap}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">市盈率</div>
                        <div class="detail-value">${item.pe}</div>
                    </div>
                </div>
                <div class="item-actions">
                    <button class="action-btn" data-action="watch" data-code="${item.code}">关注</button>
                    <button class="action-btn" data-action="compare" data-code="${item.code}">对比</button>
                    <button class="action-btn primary" data-action="trade" data-code="${item.code}">交易</button>
                </div>
            </div>
        `;
    }

    // 创建图表项目HTML
    createChartItemHTML(item, changeClass, changeSign) {
        return `
            <div class="content-item chart-item" data-code="${item.code}">
                <div class="item-header">
                    <div class="item-info">
                        <h3 class="item-name">${item.name}</h3>
                        <span class="item-code">${item.code}</span>
                    </div>
                    <div>
                        <div class="item-price">$${item.price}</div>
                        <div class="item-change ${changeClass}">
                            ${changeSign}${item.change} (${changeSign}${item.changePercent}%)
                        </div>
                    </div>
                </div>
                <div class="chart-container">
                    📈 图表区域 (${item.code})
                </div>
                <div class="item-actions">
                    <button class="action-btn" data-action="watch" data-code="${item.code}">关注</button>
                    <button class="action-btn primary" data-action="trade" data-code="${item.code}">交易</button>
                </div>
            </div>
        `;
    }

    // 处理项目点击
    handleItemClick(e) {
        if (e.target.matches('.action-btn')) {
            return; // 操作按钮有自己的处理逻辑
        }
        
        const item = e.target.closest('.content-item');
        const code = item?.dataset.code;
        
        if (code) {
            this.viewDetails(code);
        }
    }

    // 处理操作按钮点击
    handleActionClick(e) {
        e.stopPropagation();
        
        const action = e.target.dataset.action;
        const code = e.target.dataset.code;
        
        if (action && code) {
            this.executeAction(action, code);
        }
    }

    // 执行操作
    executeAction(action, code) {
        const actionMap = {
            watch: () => this.addToWatchlist(code),
            compare: () => this.compareItem(code),
            trade: () => this.quickTrade(code)
        };
        
        const handler = actionMap[action];
        if (handler) {
            handler();
        }
    }

    // 查看详情
    viewDetails(code) {
        console.log('查看详情:', code);
        
        // 添加到侧边栏最近访问
        if (window.sidebarModule) {
            const item = this.data.find(d => d.code === code);
            if (item) {
                window.sidebarModule.addRecentItem({
                    symbol: item.code,
                    name: item.name,
                    price: `$${item.price}`,
                    change: item.changePercent
                });
            }
        }
        
        if (window.tradeApp) {
            window.tradeApp.showSuccessMessage(`正在加载${code}详情页面`);
        }
    }

    // 添加到关注列表
    addToWatchlist(code) {
        console.log('添加到关注列表:', code);
        
        if (window.tradeApp) {
            window.tradeApp.showSuccessMessage(`${code}已添加到关注列表`);
        }
    }

    // 对比项目
    compareItem(code) {
        console.log('对比项目:', code);
        
        if (window.tradeApp) {
            window.tradeApp.showSuccessMessage(`${code}已添加到对比列表`);
        }
    }

    // 快速交易
    quickTrade(code) {
        console.log('快速交易:', code);
        
        if (window.tradeApp) {
            window.tradeApp.showSuccessMessage(`正在打开${code}交易面板`);
        }
    }

    // 显示加载状态
    showLoading() {
        const loadingIndicator = document.getElementById('loadingIndicator');
        const contentGrid = document.getElementById('contentGrid');
        
        if (loadingIndicator) {
            loadingIndicator.classList.remove('hide');
        }
        if (contentGrid) {
            contentGrid.style.display = 'none';
        }
        
        this.isLoading = true;
    }

    // 隐藏加载状态
    hideLoading() {
        const loadingIndicator = document.getElementById('loadingIndicator');
        const contentGrid = document.getElementById('contentGrid');
        
        if (loadingIndicator) {
            loadingIndicator.classList.add('hide');
        }
        if (contentGrid) {
            contentGrid.style.display = 'grid';
        }
        
        this.isLoading = false;
    }

    // 显示空状态
    showEmptyState() {
        const emptyState = document.getElementById('emptyState');
        const contentGrid = document.getElementById('contentGrid');
        
        if (emptyState) {
            emptyState.classList.remove('hide');
        }
        if (contentGrid) {
            contentGrid.style.display = 'none';
        }
        
        this.hideLoading();
    }

    // 隐藏空状态
    hideEmptyState() {
        const emptyState = document.getElementById('emptyState');
        if (emptyState) {
            emptyState.classList.add('hide');
        }
    }

    // 处理分页点击
    handlePageClick(e) {
        e.preventDefault();
        
        if (e.target.classList.contains('disabled') || this.isLoading) {
            return;
        }
        
        const pageText = e.target.textContent.trim();
        let targetPage = this.currentPage;
        
        if (pageText.includes('上一页')) {
            targetPage = this.currentPage - 1;
        } else if (pageText.includes('下一页')) {
            targetPage = this.currentPage + 1;
        } else if (!isNaN(pageText)) {
            targetPage = parseInt(pageText);
        }
        
        this.goToPage(targetPage);
    }

    // 跳转到指定页面
    goToPage(page) {
        const totalPages = Math.ceil(this.totalItems / this.itemsPerPage);
        
        if (page < 1 || page > totalPages || this.isLoading) {
            return;
        }
        
        this.currentPage = page;
        this.updateContent({ page });
        this.updatePagination();
    }

    // 更新分页
    updatePagination() {
        const totalPages = Math.ceil(this.totalItems / this.itemsPerPage);
        const startItem = (this.currentPage - 1) * this.itemsPerPage + 1;
        const endItem = Math.min(this.currentPage * this.itemsPerPage, this.totalItems);
        
        // 更新信息显示
        const currentRange = document.getElementById('currentRange');
        const totalItemsEl = document.getElementById('totalItems');
        const resultCount = document.getElementById('resultCount');
        
        if (currentRange) {
            currentRange.textContent = `${startItem}-${endItem}`;
        }
        if (totalItemsEl) {
            totalItemsEl.textContent = this.totalItems.toLocaleString();
        }
        if (resultCount) {
            resultCount.textContent = `共找到 ${this.totalItems.toLocaleString()} 个结果`;
        }
        
        // 生成分页控件
        const paginationControls = document.getElementById('paginationControls');
        if (paginationControls) {
            paginationControls.innerHTML = this.generatePaginationHTML(this.currentPage, totalPages);
        }
    }

    // 生成分页HTML
    generatePaginationHTML(current, total) {
        let html = '';
        
        // 上一页按钮
        html += `<button class="page-btn ${current <= 1 ? 'disabled' : ''}" ${current <= 1 ? 'disabled' : ''}>
                 ‹ 上一页
                 </button>`;
        
        // 页码按钮
        const startPage = Math.max(1, current - 2);
        const endPage = Math.min(total, current + 2);
        
        if (startPage > 1) {
            html += `<button class="page-btn">1</button>`;
            if (startPage > 2) {
                html += `<span class="page-ellipsis">...</span>`;
            }
        }
        
        for (let i = startPage; i <= endPage; i++) {
            html += `<button class="page-btn ${i === current ? 'active' : ''}">${i}</button>`;
        }
        
        if (endPage < total) {
            if (endPage < total - 1) {
                html += `<span class="page-ellipsis">...</span>`;
            }
            html += `<button class="page-btn">${total}</button>`;
        }
        
        // 下一页按钮
        html += `<button class="page-btn ${current >= total ? 'disabled' : ''}" ${current >= total ? 'disabled' : ''}>
                 下一页 ›
                 </button>`;
        
        return html;
    }

    // 重新加载
    retryLoad() {
        this.loadInitialData();
    }
}

// 导出模块供全局使用
window.ContentModule = ContentModule;

// 将方法暴露到全局对象
if (window.tradeApp) {
    window.tradeApp.switchView = function(view) {
        if (window.contentModule) {
            window.contentModule.switchView(view);
        }
    };

    window.tradeApp.sortResults = function() {
        if (window.contentModule) {
            window.contentModule.sortResults();
        }
    };
}

// 初始化内容模块
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.contentModule = new ContentModule();
    });
} else {
    window.contentModule = new ContentModule();
}
