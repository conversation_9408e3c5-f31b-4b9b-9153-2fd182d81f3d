<!-- 侧边栏组件 -->
<div class="absolute flex z-30 bg-gray-100 sm:relative h-full shadow-3xl">
    <!-- 折叠按钮 -->
    <button class="MuiButtonBase-root MuiIconButton-root MuiIconButton-sizeSmall css-1yhwxn2" 
            tabindex="0" type="button" aria-label="collapse">
        <span class="MuiTouchRipple-root css-4mb1j7"></span>
    </button>
    
    <!-- 可折叠区域 -->
    <div class="MuiCollapse-root MuiCollapse-horizontal MuiCollapse-entered css-3ihs36" 
         style="min-width:0px;width:auto;transition-duration:300ms">
        <div class="MuiCollapse-wrapper MuiCollapse-horizontal css-x6qbe8">
            <div class="MuiCollapse-wrapperInner MuiCollapse-horizontal css-10uzxfh">
                <div class="min-w-max h-full flex flex-col items-center flex-shrink-0 min-h-[667px] MuiBox-root css-0">
                    
                    <!-- 导航按钮区域 -->
                    <div class="w-full grid grid-cols-2 md:grid md:grid-cols-2 flex flex-col gap-3 py-4 px-4 flex-shrink-0 overflow-auto md:overflow-visible max-h-[50%] MuiBox-root css-0">
                        <button class="MuiButtonBase-root MuiButton-root MuiButton-default MuiButton-defaultPrimary MuiButton-sizeSmall MuiButton-defaultSizeSmall MuiButton-colorPrimary css-1kmicce" 
                                tabindex="0" type="button">探索</button>
                        
                        <a href="https://aifuck.cc/apps">
                            <button class="MuiButtonBase-root MuiButton-root MuiButton-default MuiButton-defaultPrimary MuiButton-sizeSmall MuiButton-defaultSizeSmall MuiButton-colorPrimary css-14cwvbn" 
                                    tabindex="0" type="button">创作</button>
                        </a>
                        
                        <button class="MuiButtonBase-root MuiButton-root MuiButton-default MuiButton-defaultPrimary MuiButton-sizeSmall MuiButton-defaultSizeSmall MuiButton-colorPrimary css-wx9h0v" 
                                tabindex="0" type="button">充值</button>
                        
                        <a href="https://aifuck.cc/books">
                            <button class="MuiButtonBase-root MuiButton-root MuiButton-default MuiButton-defaultPrimary MuiButton-sizeSmall MuiButton-defaultSizeSmall MuiButton-colorPrimary css-14cwvbn" 
                                    tabindex="0" type="button">小说</button>
                        </a>
                        
                        <a href="https://aifuck.cc/sections">
                            <button class="MuiButtonBase-root MuiButton-root MuiButton-default MuiButton-defaultPrimary MuiButton-sizeSmall MuiButton-defaultSizeSmall MuiButton-colorPrimary css-14cwvbn" 
                                    tabindex="0" type="button">分区</button>
                        </a>
                        
                        <!-- 更多导航按钮... -->
                        <button class="MuiButtonBase-root MuiButton-root MuiButton-default MuiButton-defaultPrimary MuiButton-sizeSmall MuiButton-defaultSizeSmall MuiButton-colorPrimary css-16vkxh5" 
                                tabindex="0" type="button">公告</button>
                        
                        <button class="MuiButtonBase-root MuiButton-root MuiButton-default MuiButton-defaultPrimary MuiButton-sizeSmall MuiButton-defaultSizeSmall MuiButton-colorPrimary css-16vkxh5" 
                                tabindex="0" type="button">我的关注</button>
                        
                        <button class="MuiButtonBase-root MuiButton-root MuiButton-default MuiButton-defaultPrimary MuiButton-sizeSmall MuiButton-defaultSizeSmall MuiButton-colorPrimary css-16vkxh5" 
                                tabindex="0" type="button">
                            <div class="absolute -top-1 -right-1 min-w-[16px] h-4 px-1 rounded-full bg-red-500 text-white text-xs flex items-center justify-center">1</div>
                            消息
                        </button>
                        
                        <a target="_blank" href="https://aifuck.cc/download">
                            <button class="MuiButtonBase-root MuiButton-root MuiButton-default MuiButton-defaultPrimary MuiButton-sizeSmall MuiButton-defaultSizeSmall MuiButton-colorPrimary css-14cwvbn" 
                                    tabindex="0" type="button">客户端下载</button>
                        </a>
                    </div>
                    
                    <!-- 历史记录按钮 -->
                    <div class="flex gap-3 w-full px-4 mb-3 MuiBox-root css-0">
                        <button class="MuiButtonBase-root MuiButton-root MuiButton-default MuiButton-defaultPrimary MuiButton-sizeSmall MuiButton-defaultSizeSmall MuiButton-colorPrimary css-xuqt8d" 
                                tabindex="0" type="button">
                            <div class="flex w-full h-full justify-between items-center history-close MuiBox-root css-0">
                                <div class="text-[var(--text-secondary)] text-xs MuiBox-root css-0">历史游玩记录</div>
                                <div class="text-[var(--primary-color)] text-xs MuiBox-root css-0"></div>
                            </div>
                        </button>
                    </div>
                    
                    <!-- 用户信息区域 -->
                    <div class="text-center mt-auto">
                        <div class="my-2">
                            <div class="my-3 flex justify-around items-center">
                                <div class="flex items-center MuiBox-root css-0">
                                    <button class="MuiButtonBase-root MuiIconButton-root MuiIconButton-sizeSmall cursor-pointer css-12lglt2" 
                                            tabindex="0" type="button"></button>
                                </div>
                                <div class="cursor-pointer hover:opacity-70 flex items-center justify-center"></div>
                                <button class="MuiButtonBase-root MuiIconButton-root MuiIconButton-sizeSmall cursor-pointer css-12lglt2" 
                                        tabindex="0" type="button"></button>
                            </div>
                            
                            <!-- 积分显示 -->
                            <div class="border-[#E0F2FE] hover:border-[#B9E6FE] bg-[#E0F2FE] text-[#026AA2] flex items-center h-[22px] px-2 rounded-md border text-xs font-semibold uppercase cursor-pointer">
                                <span class="hidden sm:block">积分：</span>243312
                            </div>
                        </div>
                        
                        <!-- 用户菜单 -->
                        <div>
                            <div class="relative inline-block text-left" data-headlessui-state="">
                                <button class="inline-flex items-center rounded-[20px] py-1 text-sm text-gray-700 hover:bg-gray-200 px-3" 
                                        id="headlessui-menu-button-:rk:" type="button" aria-haspopup="menu" aria-expanded="false">
                                    <img class="shrink-0 flex items-center rounded-full sm:mr-2 mr-0 img-mode" 
                                         alt="start5566" src="data:," style="width:32px;height:32px;font-size:32px;line-height:32px">
                                    start5566
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 侧边栏功能JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // 折叠/展开功能
    const collapseBtn = document.querySelector('[aria-label="collapse"]');
    const collapsibleArea = document.querySelector('.MuiCollapse-root');
    
    collapseBtn.addEventListener('click', function() {
        collapsibleArea.classList.toggle('MuiCollapse-entered');
    });
    
    // 按钮点击事件
    const buttons = document.querySelectorAll('.MuiButton-root');
    buttons.forEach(button => {
        button.addEventListener('click', function(e) {
            // 添加点击反馈效果
            const ripple = this.querySelector('.MuiTouchRipple-root');
            if (ripple) {
                ripple.style.transform = 'scale(1.1)';
                setTimeout(() => {
                    ripple.style.transform = 'scale(1)';
                }, 150);
            }
        });
    });
});
</script>
