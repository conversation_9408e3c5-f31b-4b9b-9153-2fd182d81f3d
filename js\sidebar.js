// 侧边栏模块
class SidebarModule {
    constructor() {
        this.isDarkMode = localStorage.getItem('darkMode') === 'true';
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadUserData();
        this.applyTheme();
    }

    setupEventListeners() {
        // 导航链接点击
        document.addEventListener('click', (e) => {
            if (e.target.matches('.nav-link')) {
                this.handleNavClick(e);
            }
        });

        // 快速操作按钮
        document.addEventListener('click', (e) => {
            if (e.target.matches('.quick-action-btn')) {
                this.handleQuickAction(e);
            }
        });
    }

    handleNavClick(e) {
        e.preventDefault();
        
        // 移除所有active状态
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });
        
        // 添加当前active状态
        e.target.classList.add('active');
        
        const href = e.target.getAttribute('href');
        console.log('Navigation to:', href);
        
        // 关闭移动端侧边栏
        if (window.tradeApp && window.tradeApp.isMobile) {
            window.tradeApp.closeSidebar();
        }
    }

    handleQuickAction(e) {
        const action = e.target.textContent;
        console.log('Quick action:', action);
        
        if (window.tradeApp) {
            window.tradeApp.showSuccessMessage(`执行${action}操作`);
        }
    }

    toggleTheme() {
        this.isDarkMode = !this.isDarkMode;
        localStorage.setItem('darkMode', this.isDarkMode);
        this.applyTheme();
        
        if (window.tradeApp) {
            window.tradeApp.showSuccessMessage(
                this.isDarkMode ? '已切换到夜间模式' : '已切换到日间模式'
            );
        }
    }

    applyTheme() {
        const toggle = document.querySelector('.toggle-switch');
        if (toggle) {
            toggle.classList.toggle('active', this.isDarkMode);
        }

        // 应用主题到body
        document.body.classList.toggle('dark-mode', this.isDarkMode);
    }

    async loadUserData() {
        try {
            // 模拟加载用户数据
            const userData = await this.fetchUserData();
            this.updateUserProfile(userData);
        } catch (error) {
            console.error('Failed to load user data:', error);
        }
    }

    async fetchUserData() {
        // 模拟API调用
        return new Promise(resolve => {
            setTimeout(() => {
                resolve({
                    name: '张三',
                    level: '高级投资者',
                    totalAssets: '128.5K',
                    todayReturn: '+12.5%',
                    positions: 23,
                    avatar: 'https://via.placeholder.com/48'
                });
            }, 500);
        });
    }

    updateUserProfile(userData) {
        const nameEl = document.querySelector('.user-info h3');
        const levelEl = document.querySelector('.user-info p');
        const avatarEl = document.querySelector('.user-avatar');
        
        if (nameEl) nameEl.textContent = userData.name;
        if (levelEl) levelEl.textContent = userData.level;
        if (avatarEl) avatarEl.src = userData.avatar;

        // 更新统计数据
        const statValues = document.querySelectorAll('.stat-value');
        if (statValues.length >= 3) {
            statValues[0].textContent = `￥${userData.totalAssets}`;
            statValues[1].textContent = userData.todayReturn;
            statValues[2].textContent = userData.positions;
        }
    }

    // 更新导航徽章
    updateNavBadge(navHref, count) {
        const navLink = document.querySelector(`a[href="${navHref}"]`);
        if (navLink) {
            let badge = navLink.querySelector('.nav-badge');
            if (count > 0) {
                if (!badge) {
                    badge = document.createElement('span');
                    badge.className = 'nav-badge';
                    navLink.appendChild(badge);
                }
                badge.textContent = count;
            } else if (badge) {
                badge.remove();
            }
        }
    }

    // 添加最近访问项目
    addRecentItem(item) {
        const recentSection = document.querySelector('.recent-section');
        if (!recentSection) {
            // 如果没有最近访问区域，创建一个
            this.createRecentSection();
        }

        const recentList = document.querySelector('.recent-list');
        if (recentList) {
            const listItem = document.createElement('li');
            listItem.className = 'recent-item';
            listItem.innerHTML = `
                <span class="symbol">${item.symbol}</span>
                <span>${item.name}</span>
                <span class="price">${item.price}</span>
                <span class="change ${item.change >= 0 ? 'positive' : 'negative'}">
                    ${item.change >= 0 ? '+' : ''}${item.change}%
                </span>
            `;
            
            // 添加到列表顶部
            recentList.insertBefore(listItem, recentList.firstChild);
            
            // 只保留最近10个
            const items = recentList.querySelectorAll('.recent-item');
            if (items.length > 10) {
                items[items.length - 1].remove();
            }
        }
    }

    createRecentSection() {
        const sidebar = document.querySelector('.sidebar');
        const footer = document.querySelector('.sidebar-footer');
        
        if (sidebar && footer) {
            const recentSection = document.createElement('div');
            recentSection.className = 'recent-section nav-section';
            recentSection.innerHTML = `
                <h4 class="nav-title">最近访问</h4>
                <ul class="recent-list"></ul>
            `;
            
            sidebar.insertBefore(recentSection, footer);
        }
    }
}

// 导出模块供全局使用
window.SidebarModule = SidebarModule;

// 初始化侧边栏模块
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.sidebarModule = new SidebarModule();
    });
} else {
    window.sidebarModule = new SidebarModule();
}
