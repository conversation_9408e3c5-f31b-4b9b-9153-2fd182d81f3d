/* 顶部横幅和导航样式 */
.header-banner {
    position: sticky;
    top: 0;
    z-index: 1000;
    background: #fff;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.top-banner {
    background: linear-gradient(90deg, #4CAF50, #45a049);
    color: white;
    padding: 8px 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    animation: slideDown 0.5s ease-out;
}

.banner-text {
    font-size: 14px;
    font-weight: 500;
}

.banner-close {
    position: absolute;
    right: 20px;
    background: none;
    border: none;
    color: white;
    font-size: 18px;
    cursor: pointer;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
}

.banner-close:hover {
    background-color: rgba(255,255,255,0.2);
}

.main-header {
    background: white;
    border-bottom: 1px solid #e0e0e0;
}

.header-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    align-items: center;
    height: 64px;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 12px;
}

.logo {
    width: 32px;
    height: 32px;
    border-radius: 6px;
}

.logo-section h1 {
    font-size: 20px;
    font-weight: 600;
    color: #1976d2;
    margin: 0;
}

.main-nav {
    flex: 1;
    margin-left: 40px;
}

.main-nav ul {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 32px;
}

.nav-link {
    text-decoration: none;
    color: #666;
    font-weight: 500;
    padding: 8px 0;
    position: relative;
    transition: color 0.2s;
}

.nav-link:hover,
.nav-link.active {
    color: #1976d2;
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -16px;
    left: 0;
    right: 0;
    height: 2px;
    background: #1976d2;
}

.user-actions {
    display: flex;
    align-items: center;
    gap: 16px;
}

.notification-btn {
    position: relative;
    background: none;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s;
}

.notification-btn:hover {
    background-color: #f5f5f5;
    border-color: #ccc;
}

.notification-btn .badge {
    position: absolute;
    top: -6px;
    right: -6px;
    background: #f44336;
    color: white;
    border-radius: 10px;
    font-size: 10px;
    padding: 2px 6px;
    min-width: 16px;
    text-align: center;
}

.user-menu {
    position: relative;
}

.user-avatar {
    background: none;
    border: 2px solid #e0e0e0;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    padding: 0;
    cursor: pointer;
    overflow: hidden;
    transition: border-color 0.2s;
}

.user-avatar:hover {
    border-color: #1976d2;
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.dropdown-menu {
    position: absolute;
    top: 48px;
    right: 0;
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    min-width: 160px;
    display: none;
    z-index: 1001;
}

.dropdown-menu.show {
    display: block;
    animation: fadeIn 0.2s ease-out;
}

.dropdown-menu a {
    display: block;
    padding: 12px 16px;
    text-decoration: none;
    color: #333;
    transition: background-color 0.2s;
}

.dropdown-menu a:hover {
    background-color: #f5f5f5;
}

.dropdown-menu hr {
    margin: 8px 0;
    border: none;
    border-top: 1px solid #e0e0e0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .header-container {
        padding: 0 16px;
    }
    
    .main-nav {
        display: none;
    }
    
    .logo-section h1 {
        font-size: 18px;
    }
    
    .user-actions {
        gap: 12px;
    }
}
