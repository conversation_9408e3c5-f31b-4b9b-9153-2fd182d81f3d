<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>AI风月 - 模块化测试</title>
  
  <!-- 测试所有CSS文件的加载 -->
  <link rel="stylesheet" href="styles/darkreader-inline.css" media="screen">
  <link rel="stylesheet" href="styles/darkreader-variables.css" media="screen">
  <link rel="stylesheet" href="styles/darkreader-user-agent.css" media="screen">
  <link rel="stylesheet" href="styles/main-styles.css">
  <link rel="stylesheet" href="styles/tailwind-utilities.css">
  <link rel="stylesheet" href="styles/markdown-styles.css">
  <link rel="stylesheet" href="styles/additional-styles.css">
  <link rel="stylesheet" href="styles/mui-styles.css">
  <link rel="stylesheet" href="styles/slick-carousel-styles.css">
  
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f5f5f5;
    }
    .test-section {
      background: white;
      margin: 20px 0;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .test-title {
      color: #333;
      border-bottom: 2px solid #1e88e5;
      padding-bottom: 10px;
      margin-bottom: 15px;
    }
    .status-ok {
      color: #4caf50;
      font-weight: bold;
    }
    .status-error {
      color: #f44336;
      font-weight: bold;
    }
  </style>
</head>
<body>
  <div class="container mx-auto max-w-4xl">
    <h1 class="text-3xl font-bold text-center mb-8 text-gray-800">AI风月 模块化测试页面</h1>
    
    <!-- CSS文件加载测试 -->
    <div class="test-section">
      <h2 class="test-title">CSS文件加载测试</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <h3 class="font-semibold mb-2">Darkreader样式</h3>
          <ul class="text-sm space-y-1">
            <li class="status-ok">✓ darkreader-inline.css</li>
            <li class="status-ok">✓ darkreader-variables.css</li>
            <li class="status-ok">✓ darkreader-user-agent.css</li>
          </ul>
        </div>
        <div>
          <h3 class="font-semibold mb-2">主要样式</h3>
          <ul class="text-sm space-y-1">
            <li class="status-ok">✓ main-styles.css</li>
            <li class="status-ok">✓ tailwind-utilities.css</li>
            <li class="status-ok">✓ markdown-styles.css</li>
            <li class="status-ok">✓ additional-styles.css</li>
            <li class="status-ok">✓ mui-styles.css</li>
          </ul>
        </div>
      </div>
    </div>
    
    <!-- Tailwind CSS测试 -->
    <div class="test-section">
      <h2 class="test-title">Tailwind CSS 功能测试</h2>
      <div class="flex flex-wrap gap-4 mb-4">
        <div class="bg-blue-500 text-white px-4 py-2 rounded">蓝色按钮</div>
        <div class="bg-green-500 text-white px-4 py-2 rounded">绿色按钮</div>
        <div class="bg-red-500 text-white px-4 py-2 rounded">红色按钮</div>
      </div>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div class="bg-gray-100 p-4 rounded">网格项目 1</div>
        <div class="bg-gray-200 p-4 rounded">网格项目 2</div>
        <div class="bg-gray-300 p-4 rounded">网格项目 3</div>
      </div>
    </div>
    
    <!-- Material-UI样式测试 -->
    <div class="test-section">
      <h2 class="test-title">Material-UI 组件测试</h2>
      <div class="flex flex-wrap gap-4">
        <button class="MuiButtonBase-root MuiButton-root MuiButton-default MuiButton-defaultPrimary MuiButton-sizeSmall MuiButton-defaultSizeSmall MuiButton-colorPrimary css-1kmicce"
                tabindex="0" 
                type="button">
          MUI 主要按钮
          <span class="MuiTouchRipple-root css-4mb1j7"></span>
        </button>
        
        <button class="MuiButtonBase-root MuiButton-root MuiButton-default MuiButton-defaultPrimary MuiButton-sizeSmall MuiButton-defaultSizeSmall MuiButton-colorPrimary css-1t8l2tu"
                tabindex="0" 
                type="button">
          MUI 次要按钮
          <span class="MuiTouchRipple-root css-4mb1j7"></span>
        </button>
        
        <button class="MuiButtonBase-root MuiIconButton-root MuiIconButton-sizeSmall cursor-pointer css-12lglt2"
                tabindex="0" 
                type="button"
                title="图标按钮">
          🔍
        </button>
      </div>
    </div>
    
    <!-- Markdown样式测试 -->
    <div class="test-section">
      <h2 class="test-title">Markdown 样式测试</h2>
      <div class="markdown-body">
        <h3>这是一个Markdown标题</h3>
        <p>这是一个段落，包含<a href="#">链接</a>和<strong>粗体文本</strong>。</p>
        <ul>
          <li>列表项目 1</li>
          <li>列表项目 2</li>
          <li>列表项目 3</li>
        </ul>
        <blockquote>
          <p>这是一个引用块，用于测试Markdown样式。</p>
        </blockquote>
      </div>
    </div>
    
    <!-- 动画测试 -->
    <div class="test-section">
      <h2 class="test-title">动画效果测试</h2>
      <div class="flex gap-4 items-center">
        <div class="w-16 h-16 bg-blue-500 rounded" style="animation: custom 2s infinite;">
          自定义动画
        </div>
        <div class="text-sm text-gray-600">
          这个方块应该有淡入淡出的动画效果
        </div>
      </div>
    </div>
    
    <!-- 响应式测试 -->
    <div class="test-section">
      <h2 class="test-title">响应式设计测试</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div class="bg-purple-100 p-4 rounded text-center">
          <div class="text-sm text-gray-600">移动端: 1列</div>
          <div class="text-sm text-gray-600">平板: 2列</div>
          <div class="text-sm text-gray-600">桌面: 4列</div>
        </div>
        <div class="bg-purple-200 p-4 rounded text-center">项目 2</div>
        <div class="bg-purple-300 p-4 rounded text-center">项目 3</div>
        <div class="bg-purple-400 p-4 rounded text-center">项目 4</div>
      </div>
    </div>
    
    <!-- 新增组件测试 -->
    <div class="test-section">
      <h2 class="test-title">公告轮播组件测试</h2>
      <div class="bg-gray-100 p-4 rounded">
        <!-- 公告轮播区域 -->
        <div class="sticky bg-[#333] z-20 MuiBox-root css-2xsrct">
          <div class="MuiBox-root css-1iocz0c">
            <div class="w-[100%] md:w-[50%] mx-auto MuiBox-root css-0">
              <div class="slick-slider w-full h-8 slick-vertical slick-initialized" dir="ltr">
                <div class="slick-list">
                  <div class="slick-track">
                    <div data-index="0" class="slick-slide" tabindex="-1" aria-hidden="true">
                      <div>
                        <div class="w-full h-8 leading-8 px-4 MuiBox-root css-0" tabindex="-1">
                          <span class="MuiTypography-root MuiTypography-button css-dyhssj">关于Grok3模型价格调整及Gemini限时优惠活动的公告</span>
                        </div>
                      </div>
                    </div>
                    <div data-index="1" class="slick-slide" tabindex="-1" aria-hidden="true">
                      <div>
                        <div class="w-full h-8 leading-8 px-4 MuiBox-root css-0" tabindex="-1">
                          <span class="MuiTypography-root MuiTypography-button css-dyhssj">招募日语作品作者的公告</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 搜索区域组件测试 -->
    <div class="test-section">
      <h2 class="test-title">搜索区域组件测试</h2>
      <div class="bg-gray-100 p-4 rounded">
        <!-- 搜索和标签区域 -->
        <div class="sticky flex flex-col justify-center items-center pt-4 px-4 pb-2 gap-2 z-10 flex-wrap bg-gray-100 MuiBox-root css-wx97nc">
          <!-- 搜索栏 -->
          <div class="flex items-center gap-2 w-full md:w-4/6 justify-center mx-auto MuiBox-root css-0">
            <div class="MuiBox-root css-ove7os">
              <div class="MuiInputBase-root MuiInputBase-colorPrimary css-1v8vzfm">
                <input placeholder="搜索关键词/作者/标签"
                       class="MuiInputBase-input css-76ccrm"
                       type="text"
                       value="">
              </div>
            </div>

            <button class="MuiButtonBase-root MuiButton-root MuiButton-default MuiButton-defaultPrimary MuiButton-sizeSmall MuiButton-defaultSizeSmall MuiButton-colorPrimary css-mh2xhw"
                    tabindex="0"
                    type="button">
              搜索
            </button>

            <button class="MuiButtonBase-root MuiButton-root MuiButton-default MuiButton-defaultPrimary MuiButton-sizeSmall MuiButton-defaultSizeSmall MuiButton-colorPrimary css-mh2xhw"
                    tabindex="0"
                    type="button">
              分区
            </button>
          </div>

          <!-- 推荐标签区域 -->
          <div class="flex relative items-center gap-2 w-full md:w-4/6 justify-between MuiBox-root css-0">
            <div class="w-full overflow-hidden whitespace-nowrap flex items-center gap-2 MuiBox-root css-0">
              <div class="text-sm MuiBox-root css-0">推荐标签：</div>

              <button class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedInfo MuiButton-sizeSmall MuiButton-outlinedSizeSmall MuiButton-colorInfo css-1h1j6dl"
                      tabindex="0"
                      type="button">
                大世界
              </button>

              <button class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedInfo MuiButton-sizeSmall MuiButton-outlinedSizeSmall MuiButton-colorInfo css-1h1j6dl"
                      tabindex="0"
                      type="button">
                纯爱
              </button>

              <button class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedInfo MuiButton-sizeSmall MuiButton-outlinedSizeSmall MuiButton-colorInfo css-1h1j6dl"
                      tabindex="0"
                      type="button">
                NTR
              </button>
            </div>
          </div>

          <!-- 分类筛选区域 -->
          <div class="flex w-full md:w-4/6 mx-auto text-sm gap-2 overflow-auto whitespace-nowrap MuiBox-root css-0">
            <div class="flex flex-1 mx-auto text-sm gap-2 overflow-hidden flex-wrap whitespace-nowrap pb-1 MuiBox-root css-0">

              <button class="MuiButtonBase-root MuiButton-root MuiButton-default MuiButton-defaultInfo MuiButton-sizeSmall MuiButton-defaultSizeSmall MuiButton-colorInfo css-9ixe1h"
                      tabindex="0"
                      type="button">
                推荐 ⭐
              </button>

              <button class="MuiButtonBase-root MuiButton-root MuiButton-default MuiButton-defaultInfo MuiButton-sizeSmall MuiButton-defaultSizeSmall MuiButton-colorInfo css-9ixe1h"
                      tabindex="0"
                      type="button">
                纯聊
              </button>

              <button class="MuiButtonBase-root MuiButton-root MuiButton-default MuiButton-defaultPrimary MuiButton-sizeSmall MuiButton-defaultSizeSmall MuiButton-colorPrimary css-1i3jx8v"
                      tabindex="0"
                      type="button">
                日榜
              </button>

            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- JavaScript测试 -->
    <div class="test-section">
      <h2 class="test-title">JavaScript 功能测试</h2>
      <p class="text-sm text-gray-600 mb-4">Shadow DOM处理脚本已加载并执行</p>
      <button onclick="testJS()" class="bg-indigo-500 text-white px-4 py-2 rounded hover:bg-indigo-600" type="button">
        测试JavaScript功能
      </button>
      <div id="js-result" class="mt-2 text-sm"></div>
    </div>
  </div>
  
  <!-- 加载JavaScript -->
  <script src="scripts/shadow-dom-processor.js"></script>
  
  <script>
    function testJS() {
      const result = document.getElementById('js-result');
      result.innerHTML = '<span class="status-ok">✓ JavaScript功能正常工作！</span>';
      
      // 测试CSS变量
      const rootStyles = getComputedStyle(document.documentElement);
      const primaryColor = rootStyles.getPropertyValue('--primary-color');
      if (primaryColor) {
        result.innerHTML += '<br><span class="status-ok">✓ CSS变量加载成功</span>';
      }
      
      // 测试Tailwind类
      const testElement = document.createElement('div');
      testElement.className = 'bg-green-500';
      document.body.appendChild(testElement);
      const bgColor = getComputedStyle(testElement).backgroundColor;
      document.body.removeChild(testElement);
      
      if (bgColor && bgColor !== 'rgba(0, 0, 0, 0)') {
        result.innerHTML += '<br><span class="status-ok">✓ Tailwind CSS类正常工作</span>';
      }
    }
    
    // 页面加载完成后的检查
    document.addEventListener('DOMContentLoaded', function() {
      console.log('AI风月模块化测试页面加载完成');
      console.log('已加载的CSS文件数量:', document.styleSheets.length);
    });
  </script>
</body>
</html>
